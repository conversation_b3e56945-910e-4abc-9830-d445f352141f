"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[617],{617:(e,t,a)=>{a.d(t,{A:()=>h});var l=a(5155);a(2115);var i=a(2388),r=a(3786),s=a(2657),n=a(1976),o=a(4601),c=a(3467),d=a(7652),u=a(7591);let h=e=>{let{tool:t,onLoginRequired:a,onUnlike:h,isInLikedPage:m=!1,locale:g="en"}=e,x=(0,d.c3)("common");return(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",style:{height:"100%"},children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(u.A,{tool:t,size:"sm"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,l.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((0,c.Ef)(t.pricing)),children:(0,c.mV)(t.pricing)})]})]}),(0,l.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,l.jsx)(r.A,{className:"h-5 w-5"})})]}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:t.description}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[t.tags.slice(0,3).map((e,t)=>(0,l.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),t.tags.length>3&&(0,l.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",t.tags.length-3]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(s.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:t.views})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:t.likes})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.default,{toolId:t._id,initialLikes:t.likes,initialLiked:m,onLoginRequired:a,onUnlike:h,isInLikedPage:m}),(0,l.jsx)(i.N_,{href:"/tools/".concat(t._id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:x("view_details")})]})]})]})})}},2388:(e,t,a)=>{a.d(t,{N_:()=>s,a8:()=>o,rd:()=>c});var l=a(9984),i=a(981);let r=(0,l.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:s,redirect:n,usePathname:o,useRouter:c}=(0,i.A)(r)},3467:(e,t,a)=>{a.d(t,{$g:()=>d,Ef:()=>o,S9:()=>u,Y$:()=>n,kX:()=>l,mV:()=>c,mp:()=>m,sT:()=>h,tF:()=>g,v4:()=>s,vS:()=>i});let l={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},i=[{id:"free",title:"免费发布",description:l.FREE_LAUNCH.description,price:l.FREE_LAUNCH.displayPrice,features:l.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:l.PRIORITY_LAUNCH.description,price:l.PRIORITY_LAUNCH.displayPrice,features:l.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},s=[{value:"",label:"All Prices"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],o=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),u=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),h=()=>l.PRIORITY_LAUNCH.promotion,m=()=>{let e=l.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,a)=>{a.d(t,{default:()=>d});var l=a(5155),i=a(2115),r=a(2388),s=a(7652),n=a(2108),o=a(9911),c=a(6214);function d(e){let{toolId:t,initialLikes:a=0,initialLiked:d=!1,onLoginRequired:u,onUnlike:h,isInLikedPage:m=!1,showCount:g=!0,size:x="md"}=e,{data:p}=(0,n.useSession)(),{getToolState:f,initializeToolState:b,toggleLike:v}=(0,c.X)(),w=(0,r.a8)(),y=(0,s.c3)("common");null==w||w.startsWith("/en");let E=f(t);(0,i.useEffect)(()=>{b(t,a,d)},[t,a,d]);let k=async()=>{if(!p){null==u||u();return}if(E.loading)return;let e=E.liked;await v(t,m)&&m&&e&&h&&h(t)},N=(()=>{switch(x){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,l.jsxs)("button",{onClick:k,disabled:E.loading,className:"\n        ".concat(N.button,"\n        inline-flex items-center space-x-1\n        ").concat(E.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:y(E.liked?"unlike":"like"),children:[E.loading?(0,l.jsx)("div",{className:"".concat(N.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):E.liked?(0,l.jsx)(o.Mbv,{className:N.icon}):(0,l.jsx)(o.sOK,{className:N.icon}),g&&(0,l.jsx)("span",{className:"".concat(N.text," font-medium"),children:E.likes})]})}},6214:(e,t,a)=>{a.d(t,{LikeProvider:()=>o,X:()=>c});var l=a(5155),i=a(2115),r=a(2108);let s={liked:!1,likes:0,loading:!1},n=(0,i.createContext)(null);function o(e){let{children:t}=e,{data:a}=(0,r.useSession)(),[o,c]=(0,i.useState)({}),d=(0,i.useCallback)(e=>o[e]||s,[o]),u=(0,i.useCallback)(function(e,t){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];c(l=>l[e]?l:{...l,[e]:{liked:a,likes:t,loading:!1}})},[]),h=(0,i.useCallback)(async e=>{if(a)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let a=await t.json();a.success&&c(t=>({...t,[e]:{liked:a.data.liked,likes:a.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[a]),m=(0,i.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!a)return!1;c(t=>({...t,[e]:{...t[e]||s,loading:!0}}));try{let a=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(a.ok){let t=await a.json();if(t.success)return c(a=>({...a,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return c(t=>({...t,[e]:{...t[e]||s,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),c(t=>({...t,[e]:{...t[e]||s,loading:!1}})),!1}},[a]);return(0,i.useEffect)(()=>{a?Object.keys(o).forEach(e=>{h(e)}):c(e=>{let t={};return Object.keys(e).forEach(a=>{t[a]={...e[a],liked:!1,loading:!1}}),t})},[a]),(0,l.jsx)(n.Provider,{value:{toolStates:o,toggleLike:m,getToolState:d,initializeToolState:u,refreshToolState:h},children:t})}function c(){let e=(0,i.useContext)(n);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},7591:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(5155),i=a(9651);let r=e=>{let{tool:t,size:a}=e;return t.logo?(0,l.jsx)(i.Ay,{src:t.logo,alt:"".concat(t.name," logo"),width:"lg"===a?i.iu.toolLogoLarge.width:i.iu.toolLogo.width,height:"lg"===a?i.iu.toolLogoLarge.height:i.iu.toolLogo.height,className:"rounded-lg object-contain flex-shrink-0",sizes:"lg"===a?i.ng.toolLogoLarge:i.ng.toolLogo,placeholder:"blur"}):(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white font-bold text-lg",children:t.name.charAt(0).toUpperCase()})})}},9651:(e,t,a)=>{a.d(t,{Ay:()=>i,iu:()=>r,ng:()=>s});var l=a(5155);function i(e){let{src:t,alt:a,width:i,height:r,className:s="",priority:n=!1,fill:o=!1,sizes:c,placeholder:d="empty",blurDataURL:u,fallbackSrc:h="/images/placeholder.svg"}=e;return o?(0,l.jsx)("div",{className:"relative overflow-hidden",children:(0,l.jsx)("img",{src:t,alt:a,className:s,style:{objectFit:"contain",padding:2,width:"100%",height:"100%"}})}):(0,l.jsx)("img",{src:t,alt:a,width:i,height:r,className:s,style:{objectFit:"contain",padding:2}})}a(2115);let r={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:84,height:84},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},s={avatar:"40px",toolLogo:"52px",toolLogoLarge:"84px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}}}]);