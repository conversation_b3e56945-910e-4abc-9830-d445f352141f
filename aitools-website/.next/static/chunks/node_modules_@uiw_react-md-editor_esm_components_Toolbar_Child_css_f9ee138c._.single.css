/* [project]/node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css [app-client] (css) */
.w-md-editor-toolbar-child {
  box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color), 0 1px 1px var(--md-editor-box-shadow-color);
  background-color: var(--md-editor-background-color);
  z-index: 1;
  border-radius: 3px;
  display: none;
  position: absolute;
}

.w-md-editor-toolbar-child.active {
  display: block;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar {
  border-bottom: 0;
  border-radius: 3px;
  padding: 3px;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar ul > li {
  display: block;
}

.w-md-editor-toolbar-child .w-md-editor-toolbar ul > li button {
  width: -webkit-fill-available;
  height: initial;
  box-sizing: border-box;
  margin: 0;
  padding: 3px 4px 2px;
}

/*# sourceMappingURL=node_modules_%40uiw_react-md-editor_esm_components_Toolbar_Child_css_f9ee138c._.single.css.map*/