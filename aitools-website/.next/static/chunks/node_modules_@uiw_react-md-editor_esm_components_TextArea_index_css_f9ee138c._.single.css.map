{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@uiw/react-md-editor/esm/components/TextArea/index.css"], "sourcesContent": [".w-md-editor-area {\n  overflow: auto;\n  border-radius: 5px;\n}\n.w-md-editor-text {\n  min-height: 100%;\n  position: relative;\n  text-align: left;\n  white-space: pre-wrap;\n  word-break: keep-all;\n  overflow-wrap: break-word;\n  box-sizing: border-box;\n  padding: 10px;\n  margin: 0;\n  font-size: 14px !important;\n  line-height: 18px !important;\n  font-variant-ligatures: common-ligatures;\n}\n.w-md-editor-text-pre,\n.w-md-editor-text-input,\n.w-md-editor-text > .w-md-editor-text-pre {\n  margin: 0;\n  border: 0;\n  background: none;\n  box-sizing: inherit;\n  display: inherit;\n  font-family: inherit;\n  font-family: var(--md-editor-font-family) !important;\n  font-size: inherit;\n  font-style: inherit;\n  font-variant-ligatures: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  line-height: inherit;\n  tab-size: inherit;\n  text-indent: inherit;\n  text-rendering: inherit;\n  text-transform: inherit;\n  white-space: inherit;\n  overflow-wrap: inherit;\n  word-break: inherit;\n  word-break: normal;\n  padding: 0;\n}\n.w-md-editor-text-pre {\n  position: relative;\n  margin: 0px !important;\n  pointer-events: none;\n  background-color: transparent !important;\n}\n.w-md-editor-text-pre > code {\n  padding: 0 !important;\n  font-family: var(--md-editor-font-family) !important;\n  font-size: 14px !important;\n  line-height: 18px !important;\n}\n.w-md-editor-text-input {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  height: 100%;\n  width: 100%;\n  resize: none;\n  color: inherit;\n  overflow: hidden;\n  outline: 0;\n  padding: inherit;\n  -webkit-font-smoothing: antialiased;\n  -webkit-text-fill-color: transparent;\n}\n.w-md-editor-text-input:empty {\n  -webkit-text-fill-color: inherit !important;\n}\n.w-md-editor-text-pre,\n.w-md-editor-text-input {\n  word-wrap: pre;\n  word-break: break-word;\n  white-space: pre-wrap;\n}\n/**\n * Hack to apply on some CSS on IE10 and IE11\n */\n@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n  /**\n    * IE doesn't support '-webkit-text-fill-color'\n    * So we use 'color: transparent' to make the text transparent on IE\n    * Unlike other browsers, it doesn't affect caret color in IE\n    */\n  .w-md-editor-text-input {\n    color: transparent !important;\n  }\n  .w-md-editor-text-input::selection {\n    background-color: #accef7 !important;\n    color: transparent !important;\n  }\n}\n.w-md-editor-text-pre .punctuation {\n  color: var(--color-prettylights-syntax-comment, #8b949e) !important;\n}\n.w-md-editor-text-pre .token.url,\n.w-md-editor-text-pre .token.content {\n  color: var(--color-prettylights-syntax-constant, #0550ae) !important;\n}\n.w-md-editor-text-pre .token.title.important {\n  color: var(--color-prettylights-syntax-markup-bold, #24292f);\n}\n.w-md-editor-text-pre .token.code-block .function {\n  color: var(--color-prettylights-syntax-entity, #8250df);\n}\n.w-md-editor-text-pre .token.bold {\n  font-weight: unset !important;\n}\n.w-md-editor-text-pre .token.title {\n  line-height: unset !important;\n  font-size: unset !important;\n  font-weight: unset !important;\n}\n.w-md-editor-text-pre .token.code.keyword {\n  color: var(--color-prettylights-syntax-constant, #0550ae) !important;\n}\n.w-md-editor-text-pre .token.strike,\n.w-md-editor-text-pre .token.strike .content {\n  color: var(--color-prettylights-syntax-markup-deleted-text, #82071e) !important;\n}\n"], "names": [], "mappings": "AAAA;;;;;AAIA;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;;;;;AAcA;;;;AAGA;;;;;;AASA;EAME;;;;EAGA;;;;;;AAKF;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA", "ignoreList": [0]}}]}