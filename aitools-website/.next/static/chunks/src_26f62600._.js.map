{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,6LAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // Priority launch service pricing\n  PRIORITY_LAUNCH: {\n    // Display price (CNY)\n    displayPrice: 19.9,\n    // Original price (CNY) - for showing pre-discount price\n    originalPrice: 49.9,\n    // Stripe price (in cents)\n    stripeAmount: 1990,\n    // Original Stripe amount (in cents)\n    originalStripeAmount: 4990,\n    // Currency\n    currency: 'USD',\n    // Stripe currency code (lowercase)\n    stripeCurrency: 'usd', // Note: currently using USD for testing\n    // Product name\n    productName: 'AI Tool Priority Launch Service',\n    // Product description\n    description: 'Get your AI tool prioritized for review and featured placement',\n    // Limited-time promotion info\n    promotion: {\n      // Whether the promotion is enabled\n      enabled: true,\n      // Promotion description\n      description: 'Limited-time offer - First 100 paid users',\n      // Discount percentage\n      discountPercent: 50,\n      // Remaining slots (can be dynamically fetched from database)\n      remainingSlots: 85\n    },\n    // Feature list\n    features: [\n      'Choose any publish date',\n      'Priority review processing',\n      'Featured homepage placement',\n      'Dedicated customer support'\n    ]\n  },\n\n  // Free launch configuration\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: 'Free Launch Service',\n    description: 'Choose any publish date after one month',\n    features: [\n      'Free submission for review',\n      'Publish date: from one month later',\n      'Standard review process',\n      'Standard display placement'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: 'Free',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: ' Freemium',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: 'Paid',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: 'All Prices' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化原价显示（带删除线）\nexport const formatOriginalPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 获取促销信息\nexport const getPromotionInfo = () => {\n  return PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n};\n\n// 检查是否有促销活动\nexport const hasActivePromotion = () => {\n  const promotion = PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n  return promotion.enabled && promotion.remainingSlots > 0;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,iBAAiB;QACf,sBAAsB;QACtB,cAAc;QACd,wDAAwD;QACxD,eAAe;QACf,0BAA0B;QAC1B,cAAc;QACd,oCAAoC;QACpC,sBAAsB;QACtB,WAAW;QACX,UAAU;QACV,mCAAmC;QACnC,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,sBAAsB;QACtB,aAAa;QACb,8BAA8B;QAC9B,WAAW;YACT,mCAAmC;YACnC,SAAS;YACT,wBAAwB;YACxB,aAAa;YACb,sBAAsB;YACtB,iBAAiB;YACjB,6DAA6D;YAC7D,gBAAgB;QAClB;QACA,eAAe;QACf,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,4BAA4B;IAC5B,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,sBAAsB,CAAC,OAAe;IACjD,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,mBAAmB;IAC9B,OAAO,eAAe,eAAe,CAAC,SAAS;AACjD;AAGO,MAAM,qBAAqB;IAChC,MAAM,YAAY,eAAe,eAAe,CAAC,SAAS;IAC1D,OAAO,UAAU,OAAO,IAAI,UAAU,cAAc,GAAG;AACzD;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts"], "sourcesContent": ["// AI工具应用的预定义标签列表 - 精选最流行的50个标签\n// 标签键值，用于国际化\nexport const AVAILABLE_TAG_KEYS = [\n  // 核心AI功能\n  'ai_assistant', 'chatgpt', 'conversational_ai', 'smart_qa', 'language_model',\n\n  // 内容创作\n  'writing_assistant', 'content_generation', 'copywriting', 'blog_writing', 'marketing_copy',\n\n  // 图像处理\n  'image_generation', 'image_editing', 'ai_painting', 'avatar_generation', 'background_removal',\n\n  // 视频处理\n  'video_generation', 'video_editing', 'video_clipping', 'short_video_creation', 'video_subtitles',\n\n  // 音频处理\n  'speech_synthesis', 'speech_recognition', 'music_generation', 'speech_to_text', 'text_to_speech',\n\n  // 代码开发\n  'code_generation', 'code_completion', 'code_review', 'development_assistant', 'low_code_platform',\n\n  // 数据分析\n  'data_analysis', 'data_visualization', 'business_intelligence', 'machine_learning', 'deep_learning',\n\n  // 办公效率\n  'office_automation', 'document_processing', 'project_management', 'team_collaboration', 'note_taking',\n\n  // 设计工具\n  'ui_design', 'logo_design', 'web_design', 'graphic_design', 'prototype_design',\n\n  // 营销工具\n  'seo_optimization', 'social_media_marketing', 'email_marketing', 'content_marketing', 'market_analysis',\n\n  // 翻译工具\n  'machine_translation', 'real_time_translation', 'document_translation', 'voice_translation'\n];\n\n// 向后兼容的中文标签列表（已弃用，建议使用国际化版本）\nexport const AVAILABLE_TAGS = [\n  // 核心AI功能\n  'AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型',\n\n  // 内容创作\n  '写作助手', '内容生成', '文案创作', '博客写作', '营销文案',\n\n  // 图像处理\n  '图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除',\n\n  // 视频处理\n  '视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕',\n\n  // 音频处理\n  '语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音',\n\n  // 代码开发\n  '代码生成', '代码补全', '代码审查', '开发助手', '低代码平台',\n\n  // 数据分析\n  '数据分析', '数据可视化', '商业智能', '机器学习', '深度学习',\n\n  // 办公效率\n  '办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具',\n\n  // 设计工具\n  'UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计',\n\n  // 营销工具\n  'SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析',\n\n  // 翻译工具\n  '机器翻译', '实时翻译', '文档翻译', '语音翻译'\n];\n\n// 标签的最大选择数量\nexport const MAX_TAGS_COUNT = 3;\n\n// 按分类组织的标签键值（用于国际化）\nexport const TAG_KEYS_BY_CATEGORY = {\n  'core_ai': ['ai_assistant', 'chatgpt', 'conversational_ai', 'smart_qa', 'language_model'],\n  'content_creation': ['writing_assistant', 'content_generation', 'copywriting', 'blog_writing', 'marketing_copy'],\n  'image_processing': ['image_generation', 'image_editing', 'ai_painting', 'avatar_generation', 'background_removal'],\n  'video_processing': ['video_generation', 'video_editing', 'video_clipping', 'short_video_creation', 'video_subtitles'],\n  'audio_processing': ['speech_synthesis', 'speech_recognition', 'music_generation', 'speech_to_text', 'text_to_speech'],\n  'code_development': ['code_generation', 'code_completion', 'code_review', 'development_assistant', 'low_code_platform'],\n  'data_analysis': ['data_analysis', 'data_visualization', 'business_intelligence', 'machine_learning', 'deep_learning'],\n  'office_productivity': ['office_automation', 'document_processing', 'project_management', 'team_collaboration', 'note_taking'],\n  'design_tools': ['ui_design', 'logo_design', 'web_design', 'graphic_design', 'prototype_design'],\n  'marketing_tools': ['seo_optimization', 'social_media_marketing', 'email_marketing', 'content_marketing', 'market_analysis'],\n  'translation_tools': ['machine_translation', 'real_time_translation', 'document_translation', 'voice_translation']\n};\n\n// 向后兼容的中文分类标签（已弃用，建议使用国际化版本）\nexport const TAGS_BY_CATEGORY = {\n  '核心AI功能': ['AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型'],\n  '内容创作': ['写作助手', '内容生成', '文案创作', '博客写作', '营销文案'],\n  '图像处理': ['图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除'],\n  '视频处理': ['视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕'],\n  '音频处理': ['语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音'],\n  '代码开发': ['代码生成', '代码补全', '代码审查', '开发助手', '低代码平台'],\n  '数据分析': ['数据分析', '数据可视化', '商业智能', '机器学习', '深度学习'],\n  '办公效率': ['办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具'],\n  '设计工具': ['UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计'],\n  '营销工具': ['SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析'],\n  '翻译工具': ['机器翻译', '实时翻译', '文档翻译', '语音翻译']\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,aAAa;;;;;;;;AACN,MAAM,qBAAqB;IAChC,SAAS;IACT;IAAgB;IAAW;IAAqB;IAAY;IAE5D,OAAO;IACP;IAAqB;IAAsB;IAAe;IAAgB;IAE1E,OAAO;IACP;IAAoB;IAAiB;IAAe;IAAqB;IAEzE,OAAO;IACP;IAAoB;IAAiB;IAAkB;IAAwB;IAE/E,OAAO;IACP;IAAoB;IAAsB;IAAoB;IAAkB;IAEhF,OAAO;IACP;IAAmB;IAAmB;IAAe;IAAyB;IAE9E,OAAO;IACP;IAAiB;IAAsB;IAAyB;IAAoB;IAEpF,OAAO;IACP;IAAqB;IAAuB;IAAsB;IAAsB;IAExF,OAAO;IACP;IAAa;IAAe;IAAc;IAAkB;IAE5D,OAAO;IACP;IAAoB;IAA0B;IAAmB;IAAqB;IAEtF,OAAO;IACP;IAAuB;IAAyB;IAAwB;CACzE;AAGM,MAAM,iBAAiB;IAC5B,SAAS;IACT;IAAQ;IAAW;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAS;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAS;IAAQ;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAQ;IAAU;IAAQ;IAAQ;IAElC,OAAO;IACP;IAAS;IAAU;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;CACzB;AAGM,MAAM,iBAAiB;AAGvB,MAAM,uBAAuB;IAClC,WAAW;QAAC;QAAgB;QAAW;QAAqB;QAAY;KAAiB;IACzF,oBAAoB;QAAC;QAAqB;QAAsB;QAAe;QAAgB;KAAiB;IAChH,oBAAoB;QAAC;QAAoB;QAAiB;QAAe;QAAqB;KAAqB;IACnH,oBAAoB;QAAC;QAAoB;QAAiB;QAAkB;QAAwB;KAAkB;IACtH,oBAAoB;QAAC;QAAoB;QAAsB;QAAoB;QAAkB;KAAiB;IACtH,oBAAoB;QAAC;QAAmB;QAAmB;QAAe;QAAyB;KAAoB;IACvH,iBAAiB;QAAC;QAAiB;QAAsB;QAAyB;QAAoB;KAAgB;IACtH,uBAAuB;QAAC;QAAqB;QAAuB;QAAsB;QAAsB;KAAc;IAC9H,gBAAgB;QAAC;QAAa;QAAe;QAAc;QAAkB;KAAmB;IAChG,mBAAmB;QAAC;QAAoB;QAA0B;QAAmB;QAAqB;KAAkB;IAC5H,qBAAqB;QAAC;QAAuB;QAAyB;QAAwB;KAAoB;AACpH;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QAAC;QAAQ;QAAW;QAAQ;QAAQ;KAAO;IACrD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IAClD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IACjD,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAS;QAAQ;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAClD,QAAQ;QAAC;QAAS;QAAU;QAAQ;QAAQ;KAAO;IACnD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;KAAO;AAC1C", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts"], "sourcesContent": ["// 国际化标签配置文件\n// 支持多语言的AI工具标签配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\nimport { AVAILABLE_TAG_KEYS, TAG_KEYS_BY_CATEGORY } from './tags';\n\nexport interface TagOption {\n  key: string;\n  label: string;\n}\n\nexport interface TagCategory {\n  key: string;\n  name: string;\n  tags: TagOption[];\n}\n\n// 客户端钩子：获取国际化的标签选项\nexport function useTagOptions(): TagOption[] {\n  const t = useTranslations('tags');\n  \n  return AVAILABLE_TAG_KEYS.map(key => ({\n    key,\n    label: t(key)\n  }));\n}\n\n// 客户端钩子：获取按分类组织的国际化标签\nexport function useTagsByCategory(): TagCategory[] {\n  const t = useTranslations('tags');\n  const categoryT = useTranslations('tag_categories');\n  \n  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({\n    key: categoryKey,\n    name: categoryT(categoryKey),\n    tags: tagKeys.map(tagKey => ({\n      key: tagKey,\n      label: t(tagKey)\n    }))\n  }));\n}\n\n// 客户端钩子：获取单个标签的翻译\nexport function useTagLabel(tagKey: string): string {\n  const t = useTranslations('tags');\n  return t(tagKey) || tagKey;\n}\n\n// 客户端钩子：获取标签分类名称\nexport function useTagCategoryName(categoryKey: string): string {\n  const t = useTranslations('tag_categories');\n  return t(categoryKey) || categoryKey;\n}\n\n// 获取所有可用的标签键值（用于验证）\nexport function getAvailableTagKeys(): string[] {\n  return AVAILABLE_TAG_KEYS;\n}\n\n// 获取所有可用的标签分类键值（用于验证）\nexport function getAvailableTagCategoryKeys(): string[] {\n  return Object.keys(TAG_KEYS_BY_CATEGORY);\n}\n\n// 服务器端函数：获取国际化的标签选项\nexport async function getTagOptions(locale?: string): Promise<TagOption[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n\n  return AVAILABLE_TAG_KEYS.map(key => ({\n    key,\n    label: t(key)\n  }));\n}\n\n// 服务器端函数：获取按分类组织的国际化标签\nexport async function getTagsByCategory(locale?: string): Promise<TagCategory[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n  const categoryT = await getTranslations({ locale: locale || 'en', namespace: 'tag_categories' });\n\n  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({\n    key: categoryKey,\n    name: categoryT(categoryKey),\n    tags: tagKeys.map(tagKey => ({\n      key: tagKey,\n      label: t(tagKey)\n    }))\n  }));\n}\n\n// 服务器端函数：获取单个标签的翻译\nexport async function getTagLabel(tagKey: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n  return t(tagKey) || tagKey;\n}\n\n// 服务器端函数：获取标签分类名称\nexport async function getTagCategoryName(categoryKey: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tag_categories' });\n  return t(categoryKey) || categoryKey;\n}\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;AAEjB;AACA;AACA;;;;;AAcO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,2HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YACpC;YACA,OAAO,EAAE;QACX,CAAC;AACH;GAPgB;;QACJ,yMAAA,CAAA,kBAAe;;;AASpB,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAElC,OAAO,OAAO,OAAO,CAAC,2HAAA,CAAA,uBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAK,CAAC;YAC3E,KAAK;YACL,MAAM,UAAU;YAChB,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE;gBACX,CAAC;QACH,CAAC;AACH;IAZgB;;QACJ,yMAAA,CAAA,kBAAe;QACP,yMAAA,CAAA,kBAAe;;;AAa5B,SAAS,YAAY,MAAc;;IACxC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,WAAW;AACtB;IAHgB;;QACJ,yMAAA,CAAA,kBAAe;;;AAKpB,SAAS,mBAAmB,WAAmB;;IACpD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,gBAAgB;AAC3B;IAHgB;;QACJ,yMAAA,CAAA,kBAAe;;;AAKpB,SAAS;IACd,OAAO,2HAAA,CAAA,qBAAkB;AAC3B;AAGO,SAAS;IACd,OAAO,OAAO,IAAI,CAAC,2HAAA,CAAA,uBAAoB;AACzC;AAGO,eAAe,cAAc,MAAe;IACjD,MAAM,IAAI,MAAM,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAE5E,OAAO,2HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YACpC;YACA,OAAO,EAAE;QACX,CAAC;AACH;AAGO,eAAe,kBAAkB,MAAe;IACrD,MAAM,IAAI,MAAM,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAC5E,MAAM,YAAY,MAAM,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAiB;IAE9F,OAAO,OAAO,OAAO,CAAC,2HAAA,CAAA,uBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAK,CAAC;YAC3E,KAAK;YACL,MAAM,UAAU;YAChB,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE;gBACX,CAAC;QACH,CAAC;AACH;AAGO,eAAe,YAAY,MAAc,EAAE,MAAe;IAC/D,MAAM,IAAI,MAAM,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAC5E,OAAO,EAAE,WAAW;AACtB;AAGO,eAAe,mBAAmB,WAAmB,EAAE,MAAe;IAC3E,MAAM,IAAI,MAAM,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAiB;IACtF,OAAO,EAAE,gBAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport { Tag, Search, X } from 'lucide-react';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport { useTagOptions } from '@/constants/tags-i18n';\nimport { Locale } from '@/i18n/config';\n\ninterface TagSelectorProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  maxTags?: number;\n  placeholder?: string;\n}\n\nexport default function TagSelector({\n  selectedTags,\n  onTagsChange,\n  maxTags = MAX_TAGS_COUNT,\n  placeholder\n}: TagSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n\n  const pathname = usePathname();\n  const t = useTranslations('common');\n  const tagOptions = useTagOptions();\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const toggleTag = (tagKey: string) => {\n    if (selectedTags.includes(tagKey)) {\n      onTagsChange(selectedTags.filter(t => t !== tagKey));\n    } else if (selectedTags.length < maxTags) {\n      onTagsChange([...selectedTags, tagKey]);\n    }\n  };\n\n  const removeTag = (tagKey: string) => {\n    onTagsChange(selectedTags.filter(t => t !== tagKey));\n  };\n\n  // 过滤标签：根据搜索词过滤，并排除已选择的标签\n  const filteredTags = tagOptions.filter(tag =>\n    tag.label.toLowerCase().includes(searchTerm.toLowerCase()) &&\n    !selectedTags.includes(tag.key)\n  );\n\n  // 获取已选择标签的显示文本\n  const getSelectedTagLabel = (tagKey: string) => {\n    const tagOption = tagOptions.find(tag => tag.key === tagKey);\n    return tagOption ? tagOption.label : tagKey;\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 标题和计数器 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{t('select_tags')}</h3>\n        <span className=\"text-sm text-gray-500\">\n          {t('selected_count', { count: selectedTags.length, max: maxTags })}\n        </span>\n      </div>\n\n      {/* 已选择的标签 */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-700\">{t('selected_tags')}</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tagKey) => (\n              <span\n                key={tagKey}\n                className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n              >\n                {getSelectedTagLabel(tagKey)}\n                <button\n                  type=\"button\"\n                  onClick={() => removeTag(tagKey)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 标签选择器 */}\n      <div className=\"space-y-3\">\n        <div className=\"relative\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('select_tags_max', { max: maxTags })}\n          </label>\n\n          {/* 搜索框 */}\n          <div className=\"relative mb-3\">\n            <input\n              type=\"text\"\n              placeholder={placeholder || t('search_tags')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onFocus={() => setIsOpen(true)}\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n          </div>\n\n          {/* 标签选择下拉框 */}\n          {(isOpen || searchTerm) && (\n            <div className=\"relative\">\n              <div className=\"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                {filteredTags.length > 0 ? (\n                  <div className=\"p-2\">\n                    <div className=\"grid grid-cols-1 gap-1\">\n                      {filteredTags.map((tag) => {\n                        const isDisabled = selectedTags.length >= maxTags;\n\n                        return (\n                          <button\n                            key={tag.key}\n                            type=\"button\"\n                            onClick={() => {\n                              toggleTag(tag.key);\n                              setSearchTerm('');\n                              setIsOpen(false);\n                            }}\n                            disabled={isDisabled}\n                            className={`\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ${isDisabled\n                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                                : 'hover:bg-blue-50 text-gray-700'\n                              }\n                            `}\n                          >\n                            <div className=\"flex items-center\">\n                              <Tag className=\"h-3 w-3 mr-2 text-gray-400\" />\n                              {tag.label}\n                            </div>\n                          </button>\n                        );\n                      })}\n                    </div>\n                    {filteredTags.length > 50 && (\n                      <p className=\"text-xs text-gray-500 mt-2 px-3\">\n                        {t('found_tags', { count: filteredTags.length })}\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"p-4 text-center text-gray-500 text-sm\">\n                    {searchTerm ? t('no_tags_found') : t('start_typing')}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭下拉框 */}\n      {(isOpen || searchTerm) && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => {\n            setIsOpen(false);\n            setSearchTerm('');\n          }}\n        />\n      )}\n\n      {/* 提示信息 */}\n      {selectedTags.length >= maxTags && (\n        <p className=\"text-sm text-amber-600\">\n          {t('max_tags_limit', { max: maxTags })}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAiBe,SAAS,YAAY,EAClC,YAAY,EACZ,YAAY,EACZ,UAAU,2HAAA,CAAA,iBAAc,EACxB,WAAW,EACM;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IAE/B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,YAAY,CAAC;QACjB,IAAI,aAAa,QAAQ,CAAC,SAAS;YACjC,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO,IAAI,aAAa,MAAM,GAAG,SAAS;YACxC,aAAa;mBAAI;gBAAc;aAAO;QACxC;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,yBAAyB;IACzB,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA,MACrC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,CAAC,aAAa,QAAQ,CAAC,IAAI,GAAG;IAGhC,eAAe;IACf,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QACrD,OAAO,YAAY,UAAU,KAAK,GAAG;IACvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,6LAAC;wBAAK,WAAU;kCACb,EAAE,kBAAkB;4BAAE,OAAO,aAAa,MAAM;4BAAE,KAAK;wBAAQ;;;;;;;;;;;;YAKnE,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;gCAEC,WAAU;;oCAET,oBAAoB;kDACrB,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCACd,EAAE,mBAAmB;gCAAE,KAAK;4BAAQ;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAa,eAAe,EAAE;oCAC9B,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,SAAS,IAAM,UAAU;oCACzB,WAAU;;;;;;8CAEZ,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAInB,CAAC,UAAU,UAAU,mBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM,GAAG,kBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,aAAa,aAAa,MAAM,IAAI;gDAE1C,qBACE,6LAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,UAAU,IAAI,GAAG;wDACjB,cAAc;wDACd,UAAU;oDACZ;oDACA,UAAU;oDACV,WAAW,CAAC;;8BAEV,EAAE,aACE,iDACA,iCACH;4BACH,CAAC;8DAED,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd,IAAI,KAAK;;;;;;;mDAlBP,IAAI,GAAG;;;;;4CAsBlB;;;;;;wCAED,aAAa,MAAM,GAAG,oBACrB,6LAAC;4CAAE,WAAU;sDACV,EAAE,cAAc;gDAAE,OAAO,aAAa,MAAM;4CAAC;;;;;;;;;;;yDAKpD,6LAAC;oCAAI,WAAU;8CACZ,aAAa,EAAE,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlD,CAAC,UAAU,UAAU,mBACpB,6LAAC;gBACC,WAAU;gBACV,SAAS;oBACP,UAAU;oBACV,cAAc;gBAChB;;;;;;YAKH,aAAa,MAAM,IAAI,yBACtB,6LAAC;gBAAE,WAAU;0BACV,EAAE,kBAAkB;oBAAE,KAAK;gBAAQ;;;;;;;;;;;;AAK9C;GAtKwB;;QASL,yHAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;QACN,mIAAA,CAAA,gBAAa;;;KAXV", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n}\n\n// 生成模糊占位符数据URL\nfunction generateBlurDataURL(): string {\n  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==';\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n}: OptimizedImageProps) {\n  const imageProps = {\n    src,\n    alt,\n    className,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <img\n          src={src}\n          alt={alt}\n          className={className}\n          style={{ objectFit: 'contain', padding: 2, width: '100%', height: '100%' }}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={src}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      style={{ objectFit: 'contain', padding: 2 }}\n    />\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 52, height: 52 },\n  toolLogoLarge: { width: 84, height: 84 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '52px',\n  toolLogoLarge: '84px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;;AAiBA,eAAe;AACf,SAAS;IACP,OAAO;AACT;AAEe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACnB;IACpB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAIA,IAAI,MAAM;QACR,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;oBAAE,WAAW;oBAAW,SAAS;oBAAG,OAAO;oBAAQ,QAAQ;gBAAO;;;;;;;;;;;IAIjF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,OAAO;YAAE,WAAW;YAAW,SAAS;QAAE;;;;;;AAGhD;KAhDwB;AAmDjB,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAI,QAAQ;IAAG;IACvC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,eAAe;IACf,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/upload-config.ts"], "sourcesContent": ["/**\n * 上传配置和字段长度限制\n */\n\n// 字段长度限制配置\nexport const FIELD_LIMITS = {\n  // 工具基本信息字段\n  TOOL_NAME: {\n    min: 2,\n    max: 40,\n    label: 'Tool Name'\n  },\n  TAGLINE: {\n    min: 4,\n    max: 80,\n    label: 'Tagline'\n  },\n  DESCRIPTION: {\n    min: 10,\n    max: 2000,\n    label: 'Description'\n  },\n  LONG_DESCRIPTION: {\n    min: 0,\n    max: 3000,\n    label: 'Long Description'\n  },\n  PRICING_DETAILS: {\n    min: 0,\n    max: 500,\n    label: 'Pricing Details'\n  },\n  WEBSITE_URL: {\n    min: 10,\n    max: 100,\n    label: 'Website URL'\n  },\n  \n  // 用户相关字段\n  USER_NAME: {\n    min: 1,\n    max: 40,\n    label: 'User Name'\n  },\n  USER_BIO: {\n    min: 0,\n    max: 1000,\n    label: 'User Bio'\n  },\n  \n  // 评论字段\n  COMMENT: {\n    min: 1,\n    max: 1000,\n    label: 'Comment'\n  }\n} as const;\n\n// 上传目录配置\nexport const UPLOAD_CONFIG = {\n  // 基础上传目录（相对于 public 目录）\n  BASE_DIR: process.env.UPLOAD_BASE_DIR || 'uploads',\n  \n  // 各类文件的子目录\n  DIRECTORIES: {\n    LOGOS: 'logos',\n    AVATARS: 'avatars',\n    SCREENSHOTS: 'screenshots',\n    TEMP: 'temp'\n  },\n  \n  // 文件大小限制（字节）\n  FILE_SIZE_LIMITS: {\n    LOGO: 5 * 1024 * 1024, // 5MB\n    AVATAR: 5 * 1024 * 1024, // 5MB\n    SCREENSHOT: 10 * 1024 * 1024, // 10MB\n  },\n  \n  // 允许的文件类型\n  ALLOWED_TYPES: {\n    IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],\n  },\n  \n  // 文件命名规则\n  NAMING: {\n    LOGO_PREFIX: 'logo_',\n    AVATAR_PREFIX: 'avatar_',\n    SCREENSHOT_PREFIX: 'screenshot_',\n    TIMESTAMP_FORMAT: 'timestamp_random' // timestamp + random string\n  }\n} as const;\n\n// 获取完整的上传目录路径\nexport function getUploadDir(subDir: keyof typeof UPLOAD_CONFIG.DIRECTORIES): string {\n  const baseDir = UPLOAD_CONFIG.BASE_DIR;\n  const subDirectory = UPLOAD_CONFIG.DIRECTORIES[subDir];\n  return `${baseDir}/${subDirectory}`;\n}\n\n// 获取文件的URL路径\nexport function getFileUrl(subDir: keyof typeof UPLOAD_CONFIG.DIRECTORIES, filename: string): string {\n  const subDirectory = UPLOAD_CONFIG.DIRECTORIES[subDir];\n  return `/api/uploads/${subDirectory}/${filename}`;\n}\n\n// 生成唯一文件名\nexport function generateFileName(\n  prefix: string,\n  originalName: string,\n  namingType: string = UPLOAD_CONFIG.NAMING.TIMESTAMP_FORMAT\n): string {\n  const timestamp = Date.now();\n  const randomString = Math.random().toString(36).substring(2, 15);\n  const fileExtension = originalName.split('.').pop();\n  \n  switch (namingType) {\n    case 'timestamp_random':\n      return `${prefix}${timestamp}_${randomString}.${fileExtension}`;\n    default:\n      return `${prefix}${timestamp}_${randomString}.${fileExtension}`;\n  }\n}\n\n// 验证字段长度\nexport function validateFieldLength(\n  fieldName: keyof typeof FIELD_LIMITS,\n  value: string\n): { isValid: boolean; error?: string } {\n  const limits = FIELD_LIMITS[fieldName];\n  if (!limits) {\n    return { isValid: true };\n  }\n  \n  const length = value.trim().length;\n  \n  if (length < limits.min) {\n    return {\n      isValid: false,\n      error: `${limits.label} must be at least ${limits.min} characters long`\n    };\n  }\n  \n  if (length > limits.max) {\n    return {\n      isValid: false,\n      error: `${limits.label} cannot exceed ${limits.max} characters`\n    };\n  }\n  \n  return { isValid: true };\n}\n\n// 验证文件大小\nexport function validateFileSize(\n  fileType: keyof typeof UPLOAD_CONFIG.FILE_SIZE_LIMITS,\n  fileSize: number\n): { isValid: boolean; error?: string } {\n  const limit = UPLOAD_CONFIG.FILE_SIZE_LIMITS[fileType];\n  \n  if (fileSize > limit) {\n    const limitMB = Math.round(limit / (1024 * 1024));\n    return {\n      isValid: false,\n      error: `File size cannot exceed ${limitMB}MB`\n    };\n  }\n  \n  return { isValid: true };\n}\n\n// 验证文件类型\nexport function validateFileType(\n  fileType: string,\n  allowedTypes: readonly string[]\n): { isValid: boolean; error?: string } {\n  if (!allowedTypes.includes(fileType)) {\n    return {\n      isValid: false,\n      error: `File type ${fileType} is not allowed. Allowed types: ${allowedTypes.join(', ')}`\n    };\n  }\n  \n  return { isValid: true };\n}\n\n\n// 验证文件上传的类型和大小\nexport function validateFileUpload(\n  file: { type: string; size: number },\n  fileCategory: keyof typeof UPLOAD_CONFIG.FILE_SIZE_LIMITS\n): { isValid: boolean; error?: string } {\n  // 验证大小\n  const sizeValidation = validateFileSize(fileCategory, file.size);\n  if (!sizeValidation.isValid) {\n    return sizeValidation;\n  }\n\n  // 根据类别选择允许的类型，目前只有 IMAGES 可选，可根据需要扩展\n  const allowedTypes = UPLOAD_CONFIG.ALLOWED_TYPES.IMAGES;\n  const typeValidation = validateFileType(file.type, allowedTypes);\n  if (!typeValidation.isValid) {\n    return typeValidation;\n  }\n\n  return { isValid: true };\n}"], "names": [], "mappings": "AAAA;;CAEC,GAED,WAAW;;;;;;;;;;;;AAyDC;AAxDL,MAAM,eAAe;IAC1B,WAAW;IACX,WAAW;QACT,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,SAAS;QACP,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,aAAa;QACX,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,kBAAkB;QAChB,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,iBAAiB;QACf,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,aAAa;QACX,KAAK;QACL,KAAK;QACL,OAAO;IACT;IAEA,SAAS;IACT,WAAW;QACT,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,UAAU;QACR,KAAK;QACL,KAAK;QACL,OAAO;IACT;IAEA,OAAO;IACP,SAAS;QACP,KAAK;QACL,KAAK;QACL,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,eAAe,IAAI;IAEzC,WAAW;IACX,aAAa;QACX,OAAO;QACP,SAAS;QACT,aAAa;QACb,MAAM;IACR;IAEA,aAAa;IACb,kBAAkB;QAChB,MAAM,IAAI,OAAO;QACjB,QAAQ,IAAI,OAAO;QACnB,YAAY,KAAK,OAAO;IAC1B;IAEA,UAAU;IACV,eAAe;QACb,QAAQ;YAAC;YAAc;YAAa;YAAa;YAAa;SAAa;IAC7E;IAEA,SAAS;IACT,QAAQ;QACN,aAAa;QACb,eAAe;QACf,mBAAmB;QACnB,kBAAkB,mBAAmB,4BAA4B;IACnE;AACF;AAGO,SAAS,aAAa,MAA8C;IACzE,MAAM,UAAU,cAAc,QAAQ;IACtC,MAAM,eAAe,cAAc,WAAW,CAAC,OAAO;IACtD,OAAO,GAAG,QAAQ,CAAC,EAAE,cAAc;AACrC;AAGO,SAAS,WAAW,MAA8C,EAAE,QAAgB;IACzF,MAAM,eAAe,cAAc,WAAW,CAAC,OAAO;IACtD,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,UAAU;AACnD;AAGO,SAAS,iBACd,MAAc,EACd,YAAoB,EACpB,aAAqB,cAAc,MAAM,CAAC,gBAAgB;IAE1D,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,eAAe,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAC7D,MAAM,gBAAgB,aAAa,KAAK,CAAC,KAAK,GAAG;IAEjD,OAAQ;QACN,KAAK;YACH,OAAO,GAAG,SAAS,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe;QACjE;YACE,OAAO,GAAG,SAAS,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe;IACnE;AACF;AAGO,SAAS,oBACd,SAAoC,EACpC,KAAa;IAEb,MAAM,SAAS,YAAY,CAAC,UAAU;IACtC,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,MAAM,SAAS,MAAM,IAAI,GAAG,MAAM;IAElC,IAAI,SAAS,OAAO,GAAG,EAAE;QACvB,OAAO;YACL,SAAS;YACT,OAAO,GAAG,OAAO,KAAK,CAAC,kBAAkB,EAAE,OAAO,GAAG,CAAC,gBAAgB,CAAC;QACzE;IACF;IAEA,IAAI,SAAS,OAAO,GAAG,EAAE;QACvB,OAAO;YACL,SAAS;YACT,OAAO,GAAG,OAAO,KAAK,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC,WAAW,CAAC;QACjE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,SAAS,iBACd,QAAqD,EACrD,QAAgB;IAEhB,MAAM,QAAQ,cAAc,gBAAgB,CAAC,SAAS;IAEtD,IAAI,WAAW,OAAO;QACpB,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI;QAC/C,OAAO;YACL,SAAS;YACT,OAAO,CAAC,wBAAwB,EAAE,QAAQ,EAAE,CAAC;QAC/C;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,SAAS,iBACd,QAAgB,EAChB,YAA+B;IAE/B,IAAI,CAAC,aAAa,QAAQ,CAAC,WAAW;QACpC,OAAO;YACL,SAAS;YACT,OAAO,CAAC,UAAU,EAAE,SAAS,gCAAgC,EAAE,aAAa,IAAI,CAAC,OAAO;QAC1F;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAIO,SAAS,mBACd,IAAoC,EACpC,YAAyD;IAEzD,OAAO;IACP,MAAM,iBAAiB,iBAAiB,cAAc,KAAK,IAAI;IAC/D,IAAI,CAAC,eAAe,OAAO,EAAE;QAC3B,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,eAAe,cAAc,aAAa,CAAC,MAAM;IACvD,MAAM,iBAAiB,iBAAiB,KAAK,IAAI,EAAE;IACnD,IAAI,CAAC,eAAe,OAAO,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/CharacterCounter.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CharacterCounterProps {\n  current: number;\n  max: number;\n  min?: number;\n  className?: string;\n}\n\nexport default function CharacterCounter({ \n  current, \n  max, \n  min = 0, \n  className = '' \n}: CharacterCounterProps) {\n  const isOverLimit = current > max;\n  const isUnderMin = current < min;\n  const percentage = (current / max) * 100;\n  \n  // 确定颜色\n  let textColor = 'text-gray-500';\n  if (isOverLimit) {\n    textColor = 'text-red-500';\n  } else if (isUnderMin) {\n    textColor = 'text-orange-500';\n  } else if (percentage > 80) {\n    textColor = 'text-yellow-600';\n  } else {\n    textColor = 'text-green-600';\n  }\n\n  return (\n    <div className={`text-sm ${textColor} ${className}`}>\n      <span className=\"font-medium\">{current}</span>\n      <span className=\"text-gray-400\">/{max}</span>\n      {min > 0 && current < min && (\n        <span className=\"ml-2 text-orange-500\">\n          (At least {min} characters)\n        </span>\n      )}\n      {isOverLimit && (\n        <span className=\"ml-2 text-red-500\">\n          (Exceeded by {current - max} characters)\n        </span>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AASe,SAAS,iBAAiB,EACvC,OAAO,EACP,GAAG,EACH,MAAM,CAAC,EACP,YAAY,EAAE,EACQ;IACtB,MAAM,cAAc,UAAU;IAC9B,MAAM,aAAa,UAAU;IAC7B,MAAM,aAAa,AAAC,UAAU,MAAO;IAErC,OAAO;IACP,IAAI,YAAY;IAChB,IAAI,aAAa;QACf,YAAY;IACd,OAAO,IAAI,YAAY;QACrB,YAAY;IACd,OAAO,IAAI,aAAa,IAAI;QAC1B,YAAY;IACd,OAAO;QACL,YAAY;IACd;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,WAAW;;0BACjD,6LAAC;gBAAK,WAAU;0BAAe;;;;;;0BAC/B,6LAAC;gBAAK,WAAU;;oBAAgB;oBAAE;;;;;;;YACjC,MAAM,KAAK,UAAU,qBACpB,6LAAC;gBAAK,WAAU;;oBAAuB;oBAC1B;oBAAI;;;;;;;YAGlB,6BACC,6LAAC;gBAAK,WAAU;;oBAAoB;oBACpB,UAAU;oBAAI;;;;;;;;;;;;;AAKtC;KAtCwB", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, Fragment, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport LoadingSpinner from '@/components/LoadingSpinner';\n\nimport SuccessMessage from '@/components/SuccessMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';\nimport { Tool } from '@/lib/api';\nimport {\n  Upload,\n  Link as LinkIcon,\n  Info,\n  ArrowLeft,\n  Sparkles\n} from 'lucide-react';\nimport { Link } from '@/i18n/routing';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport TagSelector from '@/components/TagSelector';\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from '../ui/OptimizedImage';\nimport { Locale } from '@/i18n/config';\nimport { FIELD_LIMITS, validateFieldLength } from '@/constants/upload-config';\nimport CharacterCounter from '@/components/ui/CharacterCounter';\nimport MarkdownEditor from '@/components/MarkdownEditor';\n\ninterface CategoryOption {\n  value: string;\n  label: string;\n}\n\ninterface SubmitFormClientProps {\n  categoryOptions: CategoryOption[];\n  // 编辑模式相关props\n  isEditMode?: boolean;\n  toolId?: string;\n  initialTool?: Tool;\n}\n\nexport default function SubmitFormClient({\n  categoryOptions,\n  isEditMode = false,\n  toolId,\n  initialTool,\n}: SubmitFormClientProps) {\n  // const t = getTranslations({ locale, namespace: 'submit' });\n  const t = useTranslations('submit');\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const [tool, setTool] = useState<Tool | null>(initialTool || null);\n  const [loading, setLoading] = useState(isEditMode && !initialTool);\n  const [formData, setFormData] = useState({\n    name: '',\n    tagline: '',\n    description: '',\n    website: '',\n    logoFile: null as File | null,\n    category: '',\n    tags: [] as string[],\n    pricing: ''\n  });\n\n  const [logoPreview, setLogoPreview] = useState<string | null>(null);\n  const [logoUrl, setLogoUrl] = useState<string>('');\n  const [uploadingLogo, setUploadingLogo] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [submitMessage, setSubmitMessage] = useState('');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n  const [isGeneratingAI, setIsGeneratingAI] = useState(false);\n\n  // 获取工具信息（编辑模式）\n  useEffect(() => {\n    if (!isEditMode || !toolId || initialTool) return;\n\n    const fetchToolInfo = async () => {\n      try {\n        const response = await fetch(`/api/tools/${toolId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          const toolData = data.data;\n          setTool(toolData);\n\n          // 填充表单数据\n          setFormData({\n            name: toolData.name || '',\n            tagline: toolData.tagline || '',\n            description: toolData.description || '',\n            website: toolData.website || '',\n            logoFile: null,\n            category: toolData.category || '',\n            tags: toolData.tags || [],\n            pricing: toolData.pricing || ''\n          });\n\n          setLogoUrl(toolData.logo || '');\n          setLogoPreview(toolData.logo || '');\n        } else {\n          setSubmitStatus('error');\n          setSubmitMessage(data.message || '获取工具信息失败');\n        }\n      } catch (error) {\n        console.error('获取工具信息失败:', error);\n        setSubmitStatus('error');\n        setSubmitMessage('网络错误，请重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (session) {\n      fetchToolInfo();\n    } else if (status !== 'loading') {\n      setLoading(false);\n    }\n  }, [toolId, session, status, isEditMode, initialTool]);\n\n  // 初始化表单数据（编辑模式）\n  useEffect(() => {\n    if (isEditMode && initialTool) {\n      setTool(initialTool);\n      setFormData({\n        name: initialTool.name || '',\n        tagline: initialTool.tagline || '',\n        description: initialTool.description || '',\n        website: initialTool.website || '',\n        logoFile: null,\n        category: initialTool.category || '',\n        tags: initialTool.tags || [],\n        pricing: initialTool.pricing || ''\n      });\n      setLogoUrl(initialTool.logo || '');\n      setLogoPreview(initialTool.logo || '');\n      setLoading(false);\n    }\n  }, [isEditMode, initialTool]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      setFormData(prev => ({\n        ...prev,\n        logoFile: file\n      }));\n\n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleTagsChange = (selectedTags: string[]) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: selectedTags\n    }));\n  };\n\n  // AI生成表单信息\n  const handleGenerateAI = async () => {\n    if (!formData.website.trim()) {\n      setErrors(prev => ({ ...prev, website: '请先输入网站URL' }));\n      return;\n    }\n\n    // 验证URL格式\n    try {\n      new URL(formData.website);\n    } catch (error) {\n      setErrors(prev => ({ ...prev, website: '请输入有效的网站URL' }));\n      return;\n    }\n\n    setIsGeneratingAI(true);\n    setErrors(prev => ({ ...prev, website: '' }));\n\n    try {\n      const response = await fetch('/api/ai/generate-product-info', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ website: formData.website }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        // 自动填写表单\n        setFormData(prev => ({\n          ...prev,\n          name: data.data.name || prev.name,\n          tagline: data.data.tagline || prev.tagline,\n          description: data.data.description || prev.description,\n          category: data.data.category || prev.category,\n          tags: data.data.tags && data.data.tags.length > 0 ? data.data.tags : prev.tags,\n        }));\n\n        // 清除相关错误\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.name;\n          delete newErrors.tagline;\n          delete newErrors.description;\n          delete newErrors.category;\n          delete newErrors.tags;\n          return newErrors;\n        });\n      } else {\n        setErrors(prev => ({ ...prev, website: data.error || 'AI生成失败，请重试' }));\n      }\n    } catch (error) {\n      console.error('AI generation error:', error);\n      setErrors(prev => ({ ...prev, website: '网络错误，请重试' }));\n    } finally {\n      setIsGeneratingAI(false);\n    }\n  };\n\n  // const validateForm = () => {\n  //   const newErrors: Record<string, string> = {};\n\n  //   const requiredFields = [\n  //     { key: 'name', label: t('form.tool_name') },\n  //     { key: 'tagline', label: t('form.tagline') },\n  //     { key: 'description', label: t('form.description') },\n  //     { key: 'websiteUrl', label: t('form.website_url') },\n  //     { key: 'category', label: t('form.category') },\n  //     { key: 'pricingModel', label: t('form.pricing_model') },\n  //   ];\n  //   const missingFields = requiredFields.filter(field => !formData[field.key as keyof typeof formData]);\n\n  //   if (missingFields.length > 0) {\n  //     alert(\n  //     t('form.missing_required_fields') +\n  //     ':\\n' +\n  //     missingFields.map(field => `- ${field.label}`).join('\\n')\n  //     );\n  //     return false;\n  //   }\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) newErrors.name = t('form.tool_name') + ' is required';\n    if (!formData.description.trim()) newErrors.description = t('form.description') + ' is required';\n    if (!formData.website.trim()) newErrors.website = t('form.website_url') + ' is required';\n    if (!formData.category) newErrors.category = t('form.category') + ' is required';\n    if (!formData.pricing) newErrors.pricing = t('form.pricing_model') + ' is required';\n\n    // URL validation\n    if (formData.website && !formData.website.match(/^https?:\\/\\/.+/)) {\n      newErrors.website = t('form.website_url_placeholder');\n    }\n\n    // 验证 logo（编辑模式下如果已有logo则不强制要求新上传）\n    if (!isEditMode && !formData.logoFile) {\n      newErrors.logo = t('form.logo_required');\n    }\n\n    if (formData.tags.length === 0) {\n      newErrors.tags = t('form.tags_placeholder');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!session?.user?.email) {\n      setIsLoginModalOpen(true);\n      return;\n    }\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // 处理 logo 上传\n      let finalLogoUrl = logoUrl;\n      if (formData.logoFile) {\n        const logoFormData = new FormData();\n        logoFormData.append('logo', formData.logoFile);\n\n        const uploadResponse = await fetch('/api/upload/logo', {\n          method: 'POST',\n          body: logoFormData,\n        });\n\n        if (uploadResponse.ok) {\n          const uploadResult = await uploadResponse.json();\n          finalLogoUrl = uploadResult.data.url;\n        } else {\n          const errorData = await uploadResponse.json();\n          throw new Error(errorData.message || 'Logo upload failed');\n        }\n      }\n\n      if (isEditMode && toolId) {\n        // 编辑模式：更新工具\n        const updateData = {\n          name: formData.name,\n          tagline: formData.tagline,\n          description: formData.description,\n          website: formData.website,\n          logo: finalLogoUrl || undefined,\n          category: formData.category,\n          tags: formData.tags,\n          pricing: formData.pricing\n        };\n\n        const response = await fetch(`/api/tools/${toolId}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(updateData),\n        });\n\n        const data = await response.json();\n\n        if (data.success) {\n          setSubmitStatus('success');\n          setSubmitMessage('工具信息更新成功！');\n          // 跳转回提交的工具列表\n          setTimeout(() => {\n            router.push('/profile/submitted');\n          }, 2000);\n        } else {\n          setSubmitStatus('error');\n          setSubmitMessage(data.error || 'Update failed, please retry');\n        }\n      } else {\n        // 新建模式：提交工具\n        const submitData = {\n          name: formData.name,\n          tagline: formData.tagline,\n          description: formData.description,\n          website: formData.website,\n          logo: finalLogoUrl,\n          category: formData.category,\n          tags: formData.tags,\n          pricing: formData.pricing,\n        };\n\n        const response = await fetch('/api/tools/submit', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(submitData),\n        });\n\n        if (response.ok) {\n          const result = await response.json();\n          setSubmitStatus('success');\n          setSubmitMessage(t('form.success_message'));\n\n          // 重定向到新的工具信息提交成功页面\n          setTimeout(() => {\n            router.push(`/submit/tool-info-success?toolId=${result.data.toolId}`);\n          }, 500);\n        } else {\n          const errorData = await response.json();\n          throw new Error(errorData.message || 'Submission failed');\n        }\n      }\n    } catch (error) {\n      console.error('Submit error:', error);\n      setSubmitStatus('error');\n      setSubmitMessage((error as Error).message + '. ' +t('form.error_message'));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 加载状态\n  if (status === 'loading' || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  // 未登录状态\n  if (!session) {\n    return (\n      <Fragment>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {t('auth.login_required')}\n            </h1>\n            <p className=\"text-gray-600 mb-6\">\n              {t('auth.login_to_submit')}\n            </p>\n            <button\n              onClick={() => setIsLoginModalOpen(true)}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700\"\n            >\n              {t('auth.login')}\n            </button>\n          </div>\n        </div>\n        <LoginModal\n          isOpen={isLoginModalOpen}\n          onClose={() => setIsLoginModalOpen(false)}\n        />\n      </Fragment>\n    );\n  }\n\n  // 编辑模式下工具不存在\n  if (isEditMode && !tool) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Tool not found</h1>\n          <p className=\"text-gray-600 mb-6\">The tool you want to edit does not exist or has been deleted.</p>\n          <Link\n            href=\"/profile/submitted\"\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700\"\n          >\n            Back to Tools\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  if (submitStatus === 'success') {\n    return (\n      <div className=\"max-w-2xl mx-auto\">\n        <SuccessMessage message={submitMessage || t('form.success_message')} />\n      </div>\n    );\n  }\n\n  return (\n    <Fragment>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        {isEditMode && (\n          <div className=\"mb-8\">\n            <Link\n              href=\"/profile/submitted\"\n              className=\"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Tools\n            </Link>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Edit Tool</h1>\n            <p className=\"text-gray-600 mt-2\">\n              Update your tool information to help more users understand your product.\n            </p>\n          </div>\n        )}\n\n        {!isEditMode && (\n          <div className=\"text-center mb-8\">\n            <div className=\"flex justify-center mb-4\">\n              <div className=\"p-3 bg-blue-100 rounded-full\">\n                <Upload className=\"h-8 w-8 text-blue-600\" />\n              </div>\n            </div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              {t('title')}\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              {t('subtitle')}\n            </p>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"max-w-4xl mx-auto space-y-8\">\n          {/* 基本信息 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n              <Info className=\"h-5 w-5 mr-2 text-blue-600\" />\n              {t('form.basic_info')}\n            </h2>\n\n            {/* 官方网站 */}\n            <div className=\"mb-6\">\n              <label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.website_url')} <span className=\"text-red-500\">*</span>\n              </label>\n              <div className=\"flex gap-2\">\n                <div className=\"relative flex-1\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <LinkIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  {\n                    !isEditMode &&\n                    <input\n                      type=\"url\"\n                      id=\"website\"\n                      name=\"website\"\n                      value={formData.website}\n                      onChange={handleInputChange}\n                      placeholder={isEditMode ? \"https://example.com\" : t('form.website_url_placeholder')}\n                      maxLength={FIELD_LIMITS.WEBSITE_URL.max}\n                      className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.website ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                      required\n                    />\n                  }\n                  {\n                    isEditMode && <div className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600\">\n                      {formData.website}\n                    </div>\n                  }\n                </div>\n                {/* AI生成按钮 - 仅在非编辑模式显示 */}\n                {!isEditMode && (\n                  <button\n                    type=\"button\"\n                    onClick={handleGenerateAI}\n                    disabled={isGeneratingAI || !formData.website.trim()}\n                    className=\"px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm font-medium rounded-md hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2 whitespace-nowrap\"\n                  >\n                    {isGeneratingAI ? (\n                      <>\n                        <LoadingSpinner size=\"sm\" />\n                        生成中...\n                      </>\n                    ) : (\n                      <>\n                        <Sparkles className=\"h-4 w-4\" />\n                        AI 表单信息生成\n                      </>\n                    )}\n                  </button>\n                )}\n              </div>\n              <div className=\"mt-1 flex justify-between items-center\">\n                {!isEditMode && (\n                  <CharacterCounter\n                    current={formData.website.length}\n                    max={FIELD_LIMITS.WEBSITE_URL.max}\n                    min={FIELD_LIMITS.WEBSITE_URL.min}\n                  />\n                )}\n                {errors.website && <span className=\"text-red-500 text-sm\">{errors.website}</span>}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 工具名称 */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.tool_name')} <span className=\"text-red-500\">*</span>\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  placeholder={t('form.tool_name_placeholder')}\n                  maxLength={FIELD_LIMITS.TOOL_NAME.max}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                />\n                <div className=\"mt-1 flex justify-between items-center\">\n                  <CharacterCounter\n                    current={formData.name.length}\n                    max={FIELD_LIMITS.TOOL_NAME.max}\n                    min={FIELD_LIMITS.TOOL_NAME.min}\n                  />\n                  {errors.name && <span className=\"text-red-500 text-sm\">{errors.name}</span>}\n                </div>\n              </div>\n\n              {/* 工具标语 */}\n              <div>\n                <label htmlFor=\"tagline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.tagline')} <span className=\"text-red-500\">*</span>\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"tagline\"\n                  name=\"tagline\"\n                  value={formData.tagline}\n                  onChange={handleInputChange}\n                  placeholder={t('form.tagline_placeholder')}\n                  maxLength={FIELD_LIMITS.TAGLINE.max}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n                <div className=\"mt-1 flex justify-between items-center\">\n                  <CharacterCounter\n                    current={formData.tagline.length}\n                    max={FIELD_LIMITS.TAGLINE.max}\n                    min={FIELD_LIMITS.TAGLINE.min}\n                  />\n                  {errors.tagline && <span className=\"text-red-500 text-sm\">{errors.tagline}</span>}\n                </div>\n              </div>\n            </div>\n\n            {/* 详细描述 */}\n            <div className=\"mt-6\">\n              <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.description')} <span className=\"text-red-500\">*</span>\n              </label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                placeholder={t('form.description_placeholder')}\n                maxLength={FIELD_LIMITS.DESCRIPTION.max}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n              <div className=\"mt-1 flex justify-between items-center\">\n                <CharacterCounter\n                  current={formData.description.length}\n                  max={FIELD_LIMITS.DESCRIPTION.max}\n                  min={FIELD_LIMITS.DESCRIPTION.min}\n                />\n                {errors.description && <span className=\"text-red-500 text-sm\">{errors.description}</span>}\n              </div>\n            </div>\n\n            {/* Logo 上传 */}\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.logo_upload')} {!isEditMode && <span className=\"text-red-500\">*</span>}\n              </label>\n              <div className=\"flex items-start space-x-6\">\n                <div className=\"flex-1\">\n                  {\n                    // isEditMode ? (\n                    <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors\">\n                      <input\n                        type=\"file\"\n                        accept=\"image/*\"\n                        onChange={(e) => {\n                          const file = e.target.files?.[0];\n                          if (file) {\n                            handleLogoChange(e);\n                          }\n                        }}\n                        className=\"hidden\"\n                        id=\"logo-upload\"\n                        required={!isEditMode}\n                      />\n                      <label htmlFor=\"logo-upload\" className=\"cursor-pointer\">\n                        <Upload className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                        <p className=\"text-sm text-gray-600\">\n                          {uploadingLogo ? t('form.uploading') : t('form.click_to_upload')}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {t('form.logo_upload_hint')}\n                        </p>\n                      </label>\n                    </div>\n                    // ) : (\n                    //   <div className=\"relative\">\n                    //     <input\n                    //       type=\"file\"\n                    //       id=\"logo\"\n                    //       name=\"logo\"\n                    //       accept=\"image/*\"\n                    //       onChange={handleLogoChange}\n                    //       className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    //       required={!isEditMode}\n                    //     />\n                    //     <p className=\"mt-1 text-sm text-gray-500\">\n                    //       {t('form.logo_upload_hint')}\n                    //     </p>\n                    //   </div>\n                    // )\n                  }\n                  {errors.logo && <p className=\"text-red-600 text-sm mt-1\">{errors.logo}</p>}\n                </div>\n                {/* {logoPreview && (\n                  <div className=\"flex-shrink-0\">\n                    <div className={`border border-gray-300 rounded-md overflow-hidden ${'w-24 h-24'}`}>\n                      <img\n                        src={logoPreview}\n                        alt={t('form.logo_preview')}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  </div>\n                )} */}\n                {\n                  logoPreview && <OptimizedImage\n                    alt={\"app logo\"}\n                    src={logoPreview}\n                    width={ImageSizes.toolLogo.width}\n                    height={ImageSizes.toolLogo.height}\n                    className=\"rounded-lg object-cover\"\n                    sizes={ResponsiveSizes.toolLogo}\n                    placeholder=\"blur\"\n                  />\n                }\n              </div>\n            </div>\n          </div>\n\n          {/* 分类和定价 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              {t('form.category_and_pricing')}\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 工具分类 */}\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.category')} <span className=\"text-red-500\">*</span>\n                </label>\n                <select\n                  id=\"category\"\n                  name=\"category\"\n                  value={formData.category}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">{t('form.category_placeholder')}</option>\n                  {categoryOptions.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 价格模式 */}\n              <div>\n                <label htmlFor=\"pricing\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {t('form.pricing_model')} <span className=\"text-red-500\">*</span>\n                </label>\n                <select\n                  id=\"pricing\"\n                  name=\"pricing\"\n                  value={formData.pricing}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.pricing ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                  required\n                >\n                  <option value=\"\">{t('form.pricing_placeholder')}</option>\n                  {TOOL_PRICING_FORM_OPTIONS.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {t(`form.${option.value}`)}\n                    </option>\n                  ))}\n                </select>\n                {errors.pricing && <p className=\"text-red-600 text-sm mt-1\">{errors.pricing}</p>}\n              </div>\n            </div>\n\n            {/* 选择标签 */}\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.tags')} <span className=\"text-red-500\">*</span>\n              </label>\n              <TagSelector\n                selectedTags={formData.tags}\n                onTagsChange={handleTagsChange}\n                maxTags={MAX_TAGS_COUNT}\n                placeholder={t('form.tags_placeholder')}\n              />\n              {errors.tags && <p className=\"text-red-600 text-sm mt-1\">{errors.tags}</p>}\n            </div>\n          </div>\n\n          {/* 提交指南 - 仅在新建模式显示 */}\n          {!isEditMode && (\n            <div className=\"bg-blue-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-4\">\n                {t('form.guidelines_title')}\n              </h3>\n              <ul className=\"space-y-2 text-blue-800\">\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_1')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_2')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_3')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_4')}\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3\"></span>\n                  {t('form.guideline_5')}\n                </li>\n              </ul>\n            </div>\n          )}\n\n          {/* 提交按钮 */}\n          <div className={isEditMode ? \"flex justify-end\" : \"flex justify-center\"}>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${isEditMode ? 'px-8 py-3 text-base' : 'px-8 py-3 text-base'\n                }`}\n            >\n              {isSubmitting ? (\n                <Fragment>\n                  <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  {t('form.submitting')}\n                </Fragment>\n              ) : (\n                <Fragment>\n                  <Upload className=\"h-5 w-5 mr-2\" />\n                  {t('form.submit_button')}\n                </Fragment>\n              )}\n            </button>\n          </div>\n        </form>\n\n        {/* Status Messages */}\n        {submitStatus === 'error' && (\n          <div className=\"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800\">{submitMessage}</p>\n          </div>\n        )}\n      </div>\n\n      {/* 登录模态框 */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Fragment>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAEA;AACA;;;AAzBA;;;;;;;;;;;;;;;;AAyCe,SAAS,iBAAiB,EACvC,eAAe,EACf,aAAa,KAAK,EAClB,MAAM,EACN,WAAW,EACW;;IACtB,8DAA8D;IAC9D,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,eAAe;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,CAAC;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,MAAM,EAAE;QACR,SAAS;IACX;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,cAAc,CAAC,UAAU,aAAa;YAE3C,MAAM;4DAAgB;oBACpB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;wBACnD,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,KAAK,OAAO,EAAE;4BAChB,MAAM,WAAW,KAAK,IAAI;4BAC1B,QAAQ;4BAER,SAAS;4BACT,YAAY;gCACV,MAAM,SAAS,IAAI,IAAI;gCACvB,SAAS,SAAS,OAAO,IAAI;gCAC7B,aAAa,SAAS,WAAW,IAAI;gCACrC,SAAS,SAAS,OAAO,IAAI;gCAC7B,UAAU;gCACV,UAAU,SAAS,QAAQ,IAAI;gCAC/B,MAAM,SAAS,IAAI,IAAI,EAAE;gCACzB,SAAS,SAAS,OAAO,IAAI;4BAC/B;4BAEA,WAAW,SAAS,IAAI,IAAI;4BAC5B,eAAe,SAAS,IAAI,IAAI;wBAClC,OAAO;4BACL,gBAAgB;4BAChB,iBAAiB,KAAK,OAAO,IAAI;wBACnC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,gBAAgB;wBAChB,iBAAiB;oBACnB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,SAAS;gBACX;YACF,OAAO,IAAI,WAAW,WAAW;gBAC/B,WAAW;YACb;QACF;qCAAG;QAAC;QAAQ;QAAS;QAAQ;QAAY;KAAY;IAErD,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,aAAa;gBAC7B,QAAQ;gBACR,YAAY;oBACV,MAAM,YAAY,IAAI,IAAI;oBAC1B,SAAS,YAAY,OAAO,IAAI;oBAChC,aAAa,YAAY,WAAW,IAAI;oBACxC,SAAS,YAAY,OAAO,IAAI;oBAChC,UAAU;oBACV,UAAU,YAAY,QAAQ,IAAI;oBAClC,MAAM,YAAY,IAAI,IAAI,EAAE;oBAC5B,SAAS,YAAY,OAAO,IAAI;gBAClC;gBACA,WAAW,YAAY,IAAI,IAAI;gBAC/B,eAAe,YAAY,IAAI,IAAI;gBACnC,WAAW;YACb;QACF;qCAAG;QAAC;QAAY;KAAY;IAE5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;gBACZ,CAAC;YAED,OAAO;YACP,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,eAAe,EAAE,MAAM,EAAE;YAC3B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM;YACR,CAAC;IACH;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAY,CAAC;YACpD;QACF;QAEA,UAAU;QACV,IAAI;YACF,IAAI,IAAI,SAAS,OAAO;QAC1B,EAAE,OAAO,OAAO;YACd,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAc,CAAC;YACtD;QACF;QAEA,kBAAkB;QAClB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAG,CAAC;QAE3C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS,SAAS,OAAO;gBAAC;YACnD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;wBACjC,SAAS,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK,OAAO;wBAC1C,aAAa,KAAK,IAAI,CAAC,WAAW,IAAI,KAAK,WAAW;wBACtD,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,QAAQ;wBAC7C,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;oBAChF,CAAC;gBAED,SAAS;gBACT,UAAU,CAAA;oBACR,MAAM,YAAY;wBAAE,GAAG,IAAI;oBAAC;oBAC5B,OAAO,UAAU,IAAI;oBACrB,OAAO,UAAU,OAAO;oBACxB,OAAO,UAAU,WAAW;oBAC5B,OAAO,UAAU,QAAQ;oBACzB,OAAO,UAAU,IAAI;oBACrB,OAAO;gBACT;YACF,OAAO;gBACL,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS,KAAK,KAAK,IAAI;oBAAa,CAAC;YACrE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAW,CAAC;QACrD,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,+BAA+B;IAC/B,kDAAkD;IAElD,6BAA6B;IAC7B,mDAAmD;IACnD,oDAAoD;IACpD,4DAA4D;IAC5D,2DAA2D;IAC3D,sDAAsD;IACtD,+DAA+D;IAC/D,OAAO;IACP,yGAAyG;IAEzG,oCAAoC;IACpC,aAAa;IACb,0CAA0C;IAC1C,cAAc;IACd,gEAAgE;IAChE,SAAS;IACT,oBAAoB;IACpB,MAAM;IAEN,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG,EAAE,oBAAoB;QAClE,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG,EAAE,sBAAsB;QAClF,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG,EAAE,sBAAsB;QAC1E,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG,EAAE,mBAAmB;QAClE,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG,EAAE,wBAAwB;QAErE,iBAAiB;QACjB,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,mBAAmB;YACjE,UAAU,OAAO,GAAG,EAAE;QACxB;QAEA,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,SAAS,QAAQ,EAAE;YACrC,UAAU,IAAI,GAAG,EAAE;QACrB;QAEA,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAC9B,UAAU,IAAI,GAAG,EAAE;QACrB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,oBAAoB;YACpB;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,aAAa;YACb,IAAI,eAAe;YACnB,IAAI,SAAS,QAAQ,EAAE;gBACrB,MAAM,eAAe,IAAI;gBACzB,aAAa,MAAM,CAAC,QAAQ,SAAS,QAAQ;gBAE7C,MAAM,iBAAiB,MAAM,MAAM,oBAAoB;oBACrD,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,eAAe,EAAE,EAAE;oBACrB,MAAM,eAAe,MAAM,eAAe,IAAI;oBAC9C,eAAe,aAAa,IAAI,CAAC,GAAG;gBACtC,OAAO;oBACL,MAAM,YAAY,MAAM,eAAe,IAAI;oBAC3C,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;gBACvC;YACF;YAEA,IAAI,cAAc,QAAQ;gBACxB,YAAY;gBACZ,MAAM,aAAa;oBACjB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,MAAM,gBAAgB;oBACtB,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;gBAC3B;gBAEA,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;oBACnD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB;oBAChB,iBAAiB;oBACjB,aAAa;oBACb,WAAW;wBACT,OAAO,IAAI,CAAC;oBACd,GAAG;gBACL,OAAO;oBACL,gBAAgB;oBAChB,iBAAiB,KAAK,KAAK,IAAI;gBACjC;YACF,OAAO;gBACL,YAAY;gBACZ,MAAM,aAAa;oBACjB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,MAAM;oBACN,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;gBAC3B;gBAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,gBAAgB;oBAChB,iBAAiB,EAAE;oBAEnB,mBAAmB;oBACnB,WAAW;wBACT,OAAO,IAAI,CAAC,CAAC,iCAAiC,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE;oBACtE,GAAG;gBACL,OAAO;oBACL,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB;YAChB,iBAAiB,AAAC,MAAgB,OAAO,GAAG,OAAM,EAAE;QACtD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,OAAO;IACP,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,QAAQ;IACR,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,6JAAA,CAAA,WAAQ;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;;8BAIT,6LAAC,2IAAA,CAAA,UAAU;oBACT,QAAQ;oBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;IAI3C;IAEA,aAAa;IACb,IAAI,cAAc,CAAC,MAAM;QACvB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,yHAAA,CAAA,OAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,SAAS,iBAAiB,EAAE;;;;;;;;;;;IAGlD;IAEA,qBACE,6LAAC,6JAAA,CAAA,WAAQ;;0BACP,6LAAC;gBAAI,WAAU;;oBAEZ,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yHAAA,CAAA,OAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAMrC,CAAC,4BACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGtB,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAKT,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,EAAE;;;;;;;kDAIL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAU,WAAU;;oDAChC,EAAE;oDAAoB;kEAAC,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qMAAA,CAAA,OAAQ;oEAAC,WAAU;;;;;;;;;;;4DAGpB,CAAC,4BACD,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,aAAa,aAAa,wBAAwB,EAAE;gEACpD,WAAW,uIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;gEACvC,WAAW,CAAC,6HAA6H,EAAE,OAAO,OAAO,GAAG,mBAAmB,mBAC3K;gEACJ,QAAQ;;;;;;4DAIV,4BAAc,6LAAC;gEAAI,WAAU;0EAC1B,SAAS,OAAO;;;;;;;;;;;;oDAKtB,CAAC,4BACA,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,kBAAkB,CAAC,SAAS,OAAO,CAAC,IAAI;wDAClD,WAAU;kEAET,+BACC;;8EACE,6LAAC,uIAAA,CAAA,UAAc;oEAAC,MAAK;;;;;;gEAAO;;yFAI9B;;8EACE,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;0DAO1C,6LAAC;gDAAI,WAAU;;oDACZ,CAAC,4BACA,6LAAC,+IAAA,CAAA,UAAgB;wDACf,SAAS,SAAS,OAAO,CAAC,MAAM;wDAChC,KAAK,uIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;wDACjC,KAAK,uIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;;;;;;oDAGpC,OAAO,OAAO,kBAAI,6LAAC;wDAAK,WAAU;kEAAwB,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAI7E,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAO,WAAU;;4DAC7B,EAAE;4DAAkB;0EAAC,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEvD,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,aAAa,EAAE;wDACf,WAAW,uIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG;wDACrC,WAAU;wDACV,QAAQ;;;;;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+IAAA,CAAA,UAAgB;gEACf,SAAS,SAAS,IAAI,CAAC,MAAM;gEAC7B,KAAK,uIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG;gEAC/B,KAAK,uIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG;;;;;;4DAEhC,OAAO,IAAI,kBAAI,6LAAC;gEAAK,WAAU;0EAAwB,OAAO,IAAI;;;;;;;;;;;;;;;;;;0DAKvE,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;;4DAChC,EAAE;4DAAgB;0EAAC,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAErD,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,aAAa,EAAE;wDACf,WAAW,uIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,GAAG;wDACnC,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+IAAA,CAAA,UAAgB;gEACf,SAAS,SAAS,OAAO,CAAC,MAAM;gEAChC,KAAK,uIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,GAAG;gEAC7B,KAAK,uIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,GAAG;;;;;;4DAE9B,OAAO,OAAO,kBAAI,6LAAC;gEAAK,WAAU;0EAAwB,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAM/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;;oDACpC,EAAE;oDAAoB;kEAAC,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAa,EAAE;gDACf,WAAW,uIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;gDACvC,MAAM;gDACN,WAAU;gDACV,QAAQ;;;;;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+IAAA,CAAA,UAAgB;wDACf,SAAS,SAAS,WAAW,CAAC,MAAM;wDACpC,KAAK,uIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;wDACjC,KAAK,uIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;;;;;;oDAElC,OAAO,WAAW,kBAAI,6LAAC;wDAAK,WAAU;kEAAwB,OAAO,WAAW;;;;;;;;;;;;;;;;;;kDAKrF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;oDACd,EAAE;oDAAoB;oDAAE,CAAC,4BAAc,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAEX,iBAAiB;0EACjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,QAAO;wEACP,UAAU,CAAC;4EACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;4EAChC,IAAI,MAAM;gFACR,iBAAiB;4EACnB;wEACF;wEACA,WAAU;wEACV,IAAG;wEACH,UAAU,CAAC;;;;;;kFAEb,6LAAC;wEAAM,SAAQ;wEAAc,WAAU;;0FACrC,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAE,WAAU;0FACV,gBAAgB,EAAE,oBAAoB,EAAE;;;;;;0FAE3C,6LAAC;gFAAE,WAAU;0FACV,EAAE;;;;;;;;;;;;;;;;;;4DAqBV,OAAO,IAAI,kBAAI,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,IAAI;;;;;;;;;;;;oDAcrE,6BAAe,6LAAC,6IAAA,CAAA,UAAc;wDAC5B,KAAK;wDACL,KAAK;wDACL,OAAO,6IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;wDAChC,QAAQ,6IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;wDAClC,WAAU;wDACV,OAAO,6IAAA,CAAA,kBAAe,CAAC,QAAQ;wDAC/B,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAQtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAGL,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;;4DACjC,EAAE;4DAAiB;0EAAC,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtD,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;wDACV,QAAQ;;0EAER,6LAAC;gEAAO,OAAM;0EAAI,EAAE;;;;;;4DACnB,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,KAAK;mEADF,OAAO,KAAK;;;;;;;;;;;;;;;;;0DAQ/B,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;;4DAChC,EAAE;4DAAsB;0EAAC,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE3D,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAW,CAAC,uHAAuH,EAAE,OAAO,OAAO,GAAG,mBAAmB,mBACrK;wDACJ,QAAQ;;0EAER,6LAAC;gEAAO,OAAM;0EAAI,EAAE;;;;;;4DACnB,8HAAA,CAAA,4BAAyB,CAAC,GAAG,CAAC,CAAC,uBAC9B,6LAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;mEADd,OAAO,KAAK;;;;;;;;;;;oDAK5B,OAAO,OAAO,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAK/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;oDACd,EAAE;oDAAa;kEAAC,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAElD,6LAAC,oIAAA,CAAA,UAAW;gDACV,cAAc,SAAS,IAAI;gDAC3B,cAAc;gDACd,SAAS,2HAAA,CAAA,iBAAc;gDACvB,aAAa,EAAE;;;;;;4CAEhB,OAAO,IAAI,kBAAI,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;;4BAKxE,CAAC,4BACA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;0DAEL,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;;;;;;;;;;;;;;;;;;;0CAOX,6LAAC;gCAAI,WAAW,aAAa,qBAAqB;0CAChD,cAAA,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,uQAAuQ,EAAE,aAAa,wBAAwB,uBACtT;8CAEH,6BACC,6LAAC,6JAAA,CAAA,WAAQ;;0DACP,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;gDAAK,WAAU;;;;;;4CACnC,EAAE;;;;;;6DAGL,6LAAC,6JAAA,CAAA,WAAQ;;0DACP,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,EAAE;;;;;;;;;;;;;;;;;;;;;;;oBAQZ,iBAAiB,yBAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMnC,6LAAC,2IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C;GAxzBwB;;QAOZ,yMAAA,CAAA,kBAAe;QACS,iJAAA,CAAA,aAAU;QAC7B,yHAAA,CAAA,YAAS;;;KATF", "debugId": null}}]}