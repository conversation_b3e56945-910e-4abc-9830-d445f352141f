{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@uiw/react-md-editor/esm/components/Toolbar/Child.css"], "sourcesContent": [".w-md-editor-toolbar-child {\n  position: absolute;\n  border-radius: 3px;\n  box-shadow: 0 0 0 1px var(--md-editor-box-shadow-color), 0 0 0 var(--md-editor-box-shadow-color), 0 1px 1px var(--md-editor-box-shadow-color);\n  background-color: var(--md-editor-background-color);\n  z-index: 1;\n  display: none;\n}\n.w-md-editor-toolbar-child.active {\n  display: block;\n}\n.w-md-editor-toolbar-child .w-md-editor-toolbar {\n  border-bottom: 0;\n  padding: 3px;\n  border-radius: 3px;\n}\n.w-md-editor-toolbar-child .w-md-editor-toolbar ul > li {\n  display: block;\n}\n.w-md-editor-toolbar-child .w-md-editor-toolbar ul > li button {\n  width: -webkit-fill-available;\n  height: initial;\n  box-sizing: border-box;\n  padding: 3px 4px 2px 4px;\n  margin: 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AAQA;;;;AAGA;;;;;;AAKA;;;;AAGA", "ignoreList": [0]}}]}