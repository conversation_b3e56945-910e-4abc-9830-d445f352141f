(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8493],{2342:(e,t,s)=>{"use strict";s.d(t,{default:()=>E});var a=s(5155),l=s(2115),r=s(2108),i=s(2388),n=s(7652),o=s(2731),c=s(5734),d=s(6063),m=s(3467),u=s(7550),x=s(9869),g=s(1284),h=s(8164);let b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var p=s(4416),f=s(7924),j=s(3332);function y(e){let{selectedTags:t,onTagsChange:s,maxTags:r=3,placeholder:o}=e,[c,d]=(0,l.useState)(""),[m,u]=(0,l.useState)(!1),x=(0,i.a8)(),g=(0,n.c3)("common"),h=function(){let e=(0,n.c3)("tags");return b.map(t=>({key:t,label:e(t)}))}();null==x||x.startsWith("/en");let y=e=>{t.includes(e)?s(t.filter(t=>t!==e)):t.length<r&&s([...t,e])},v=e=>{s(t.filter(t=>t!==e))},N=h.filter(e=>e.label.toLowerCase().includes(c.toLowerCase())&&!t.includes(e.key)),w=e=>{let t=h.find(t=>t.key===e);return t?t.label:e};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:g("select_tags")}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:g("selected_count",{count:t.length,max:r})})]}),t.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:g("selected_tags")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[w(e),(0,a.jsx)("button",{type:"button",onClick:()=>v(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,a.jsx)(p.A,{className:"h-3 w-3"})})]},e))})]}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:g("select_tags_max",{max:r})}),(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)("input",{type:"text",placeholder:o||g("search_tags"),value:c,onChange:e=>d(e.target.value),onFocus:()=>u(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(f.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(m||c)&&(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:N.length>0?(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-1",children:N.map(e=>{let s=t.length>=r;return(0,a.jsx)("button",{type:"button",onClick:()=>{y(e.key),d(""),u(!1)},disabled:s,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(s?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e.label]})},e.key)})}),N.length>50&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:g("found_tags",{count:N.length})})]}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:g(c?"no_tags_found":"start_typing")})})})]})}),(m||c)&&(0,a.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{u(!1),d("")}}),t.length>=r&&(0,a.jsx)("p",{className:"text-sm text-amber-600",children:g("max_tags_limit",{max:r})})]})}var v=s(9651),N=s(9509);let w={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},WEBSITE_URL:{min:10,max:100,label:"Website URL"}};function _(e){let{current:t,max:s,min:l=0,className:r=""}=e,i=t>s,n=t<l,o=t/s*100,c="text-gray-500";return c=i?"text-red-500":n?"text-orange-500":o>80?"text-yellow-600":"text-green-600",(0,a.jsxs)("div",{className:"text-sm ".concat(c," ").concat(r),children:[(0,a.jsx)("span",{className:"font-medium",children:t}),(0,a.jsxs)("span",{className:"text-gray-400",children:["/",s]}),l>0&&t<l&&(0,a.jsxs)("span",{className:"ml-2 text-orange-500",children:["(At least ",l," characters)"]}),i&&(0,a.jsxs)("span",{className:"ml-2 text-red-500",children:["(Exceeded by ",t-s," characters)"]})]})}function E(e){let{categoryOptions:t,isEditMode:s=!1,toolId:b,initialTool:p}=e,f=(0,n.c3)("submit"),{data:j,status:N}=(0,r.useSession)(),E=(0,i.rd)(),[k,A]=(0,l.useState)(p||null),[C,F]=(0,l.useState)(s&&!p),[I,S]=(0,l.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[L,R]=(0,l.useState)(null),[T,P]=(0,l.useState)(""),[U,O]=(0,l.useState)(!1),[D,M]=(0,l.useState)(!1),[q,z]=(0,l.useState)("idle"),[H,Y]=(0,l.useState)(""),[B,W]=(0,l.useState)({}),[G,$]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if(!s||!b||p)return;let e=async()=>{try{let e=await fetch("/api/tools/".concat(b)),t=await e.json();if(t.success){let e=t.data;A(e),S({name:e.name||"",tagline:e.tagline||"",description:e.description||"",website:e.website||"",logoFile:null,category:e.category||"",tags:e.tags||[],pricing:e.pricing||""}),P(e.logo||""),R(e.logo||"")}else z("error"),Y(t.message||"获取工具信息失败")}catch(e){console.error("获取工具信息失败:",e),z("error"),Y("网络错误，请重试")}finally{F(!1)}};j?e():"loading"!==N&&F(!1)},[b,j,N,s,p]),(0,l.useEffect)(()=>{s&&p&&(A(p),S({name:p.name||"",tagline:p.tagline||"",description:p.description||"",website:p.website||"",logoFile:null,category:p.category||"",tags:p.tags||[],pricing:p.pricing||""}),P(p.logo||""),R(p.logo||""),F(!1))},[s,p]);let J=e=>{let{name:t,value:s}=e.target;S(e=>({...e,[t]:s}))},X=e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];if(s){S(e=>({...e,logoFile:s}));let e=new FileReader;e.onload=e=>{var t;R(null==(t=e.target)?void 0:t.result)},e.readAsDataURL(s)}},K=()=>{let e={};return I.name.trim()||(e.name=f("form.tool_name")+" is required"),I.description.trim()||(e.description=f("form.description")+" is required"),I.website.trim()||(e.website=f("form.website_url")+" is required"),I.category||(e.category=f("form.category")+" is required"),I.pricing||(e.pricing=f("form.pricing_model")+" is required"),I.website&&!I.website.match(/^https?:\/\/.+/)&&(e.website=f("form.website_url_placeholder")),s||I.logoFile||(e.logo=f("form.logo_required")),0===I.tags.length&&(e.tags=f("form.tags_placeholder")),W(e),0===Object.keys(e).length},Q=async e=>{var t;if(e.preventDefault(),!(null==j||null==(t=j.user)?void 0:t.email))return void $(!0);if(K()){M(!0),z("idle");try{let e=T;if(I.logoFile){let t=new FormData;t.append("logo",I.logoFile);let s=await fetch("/api/upload/logo",{method:"POST",body:t});if(s.ok)e=(await s.json()).data.url;else{let e=await s.json();throw Error(e.message||"Logo upload failed")}}if(s&&b){let t={name:I.name,tagline:I.tagline,description:I.description,website:I.website,logo:e||void 0,category:I.category,tags:I.tags,pricing:I.pricing},s=await fetch("/api/tools/".concat(b),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await s.json();a.success?(z("success"),Y("工具信息更新成功！"),setTimeout(()=>{E.push("/profile/submitted")},2e3)):(z("error"),Y(a.error||"Update failed, please retry"))}else{let t={name:I.name,tagline:I.tagline,description:I.description,website:I.website,logo:e,category:I.category,tags:I.tags,pricing:I.pricing},s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok){let e=await s.json();z("success"),Y(f("form.success_message")),setTimeout(()=>{E.push("/submit/tool-info-success?toolId=".concat(e.data.toolId))},500)}else{let e=await s.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),z("error"),Y(e.message+". "+f("form.error_message"))}finally{M(!1)}}};return"loading"===N||C?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(o.A,{size:"lg"})}):j?s&&!k?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,a.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===q?(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)(c.A,{message:H||f("form.success_message")})}):(0,a.jsxs)(l.Fragment,{children:[(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s&&(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)(i.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!s&&(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:f("title")}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:f("subtitle")})]}),(0,a.jsxs)("form",{onSubmit:Q,className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-blue-600"}),f("form.basic_info")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tool_name")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:I.name,onChange:J,placeholder:f("form.tool_name_placeholder"),maxLength:w.TOOL_NAME.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(_,{current:I.name.length,max:w.TOOL_NAME.max,min:w.TOOL_NAME.min}),B.name&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:B.name})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tagline")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:I.tagline,onChange:J,placeholder:f("form.tagline_placeholder"),maxLength:w.TAGLINE.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(_,{current:I.tagline.length,max:w.TAGLINE.max,min:w.TAGLINE.min}),B.tagline&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:B.tagline})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.description")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("textarea",{id:"description",name:"description",value:I.description,onChange:J,placeholder:f("form.description_placeholder"),maxLength:w.DESCRIPTION.max,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(_,{current:I.description.length,max:w.DESCRIPTION.max,min:w.DESCRIPTION.min}),B.description&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:B.description})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.website_url")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),!s&&(0,a.jsx)("input",{type:"url",id:"website",name:"website",value:I.website,onChange:J,placeholder:s?"https://example.com":f("form.website_url_placeholder"),maxLength:w.WEBSITE_URL.max,className:"w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(B.website?"border-red-300":"border-gray-300"),required:!0}),s&&(0,a.jsx)("div",{className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:I.website})]}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[!s&&(0,a.jsx)(_,{current:I.website.length,max:w.WEBSITE_URL.max,min:w.WEBSITE_URL.min}),B.website&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:B.website})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.logo_upload")," ",!s&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;(null==(t=e.target.files)?void 0:t[0])&&X(e)},className:"hidden",id:"logo-upload",required:!s}),(0,a.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:U?f("form.uploading"):f("form.click_to_upload")}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:f("form.logo_upload_hint")})]})]}),B.logo&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:B.logo})]}),L&&(0,a.jsx)(v.Ay,{alt:"app logo",src:L,width:v.iu.toolLogo.width,height:v.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:v.ng.toolLogo,placeholder:"blur"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:f("form.category_and_pricing")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.category")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("select",{id:"category",name:"category",value:I.category,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:f("form.category_placeholder")}),t.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.pricing_model")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("select",{id:"pricing",name:"pricing",value:I.pricing,onChange:J,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(B.pricing?"border-red-300":"border-gray-300"),required:!0,children:[(0,a.jsx)("option",{value:"",children:f("form.pricing_placeholder")}),m.Y$.map(e=>(0,a.jsx)("option",{value:e.value,children:f("form.".concat(e.value))},e.value))]}),B.pricing&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:B.pricing})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tags")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(y,{selectedTags:I.tags,onTagsChange:e=>{S(t=>({...t,tags:e}))},maxTags:3,placeholder:f("form.tags_placeholder")}),B.tags&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:B.tags})]})]}),!s&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:f("form.guidelines_title")}),(0,a.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_1")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_2")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_3")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_4")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_5")]})]})]}),(0,a.jsx)("div",{className:s?"flex justify-end":"flex justify-center",children:(0,a.jsx)("button",{type:"submit",disabled:D,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ".concat("px-8 py-3 text-base"),children:D?(0,a.jsxs)(l.Fragment,{children:[(0,a.jsx)(o.A,{size:"sm",className:"mr-2"}),f("form.submitting")]}):(0,a.jsxs)(l.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-5 w-5 mr-2"}),f("form.submit_button")]})})})]}),"error"===q&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-800",children:H})})]}),(0,a.jsx)(d.A,{isOpen:G,onClose:()=>$(!1)})]}):(0,a.jsxs)(l.Fragment,{children:[(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:f("auth.login_required")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:f("auth.login_to_submit")}),(0,a.jsx)("button",{onClick:()=>$(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:f("auth.login")})]})}),(0,a.jsx)(d.A,{isOpen:G,onClose:()=>$(!1)})]})}N.env.UPLOAD_BASE_DIR},2388:(e,t,s)=>{"use strict";s.d(t,{N_:()=>i,a8:()=>o,rd:()=>c});var a=s(9984),l=s(981);let r=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:i,redirect:n,usePathname:o,useRouter:c}=(0,l.A)(r)},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(5155);function l(e){let{size:t="md",className:s=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>o,S9:()=>m,Y$:()=>n,kX:()=>a,mV:()=>c,mp:()=>x,sT:()=>u,tF:()=>g,v4:()=>i,vS:()=>l});let a={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},l=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"All Prices"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],o=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),m=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),u=()=>a.PRIORITY_LAUNCH.promotion,x=()=>{let e=a.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},5734:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(5155),l=s(646),r=s(4416);function i(e){let{message:t,onClose:s,className:i=""}=e;return(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(l.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-green-800 text-sm",children:t})}),s&&(0,a.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(r.A,{className:"w-4 h-4"})})]})})}},6063:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var a=s(5155),l=s(2115),r=s(2108),i=s(2388),n=s(7652),o=s(3385),c=s(9911);function d(e){let{isOpen:t,onClose:s}=e,[d,m]=(0,l.useState)("method"),[u,x]=(0,l.useState)(""),[g,h]=(0,l.useState)(""),[b,p]=(0,l.useState)(!1),[f,j]=(0,l.useState)("");(0,i.a8)();let y=(0,n.c3)("auth");(0,o.Ym)();let v=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",s=document.createElement("div");s.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},N=()=>{m("method"),x(""),h(""),j(""),s()},w=async e=>{try{p(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){v(y("login_failed"),"error")}finally{p(!1)}},_=async()=>{if(!u)return void j(y("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u))return void j(y("email_invalid"));j(""),p(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:u})}),t=await e.json();t.success?(h(t.token),m("code"),v(y("verification_sent"))):v(t.error||y("send_failed"),"error")}catch(e){v(y("network_error"),"error")}finally{p(!1)}},E=async e=>{if(6===e.length){p(!0);try{let t=await (0,r.signIn)("email-code",{email:u,code:e,token:g,redirect:!1});(null==t?void 0:t.ok)?(v(y("login_success")),N()):v((null==t?void 0:t.error)||y("verification_error"),"error")}catch(e){v(y("network_error"),"error")}finally{p(!1)}}},k=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");if(s[e].value=t,t&&e<5){var a;null==(a=s[e+1])||a.focus()}let l=Array.from(s).map(e=>e.value).join("");6===l.length&&E(l)};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===d&&y("login_title"),"email"===d&&y("email_login_title"),"code"===d&&y("verification_title")]}),(0,a.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:y("choose_method")}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors disabled:opacity-50",onClick:()=>w("google"),disabled:b,children:[(0,a.jsx)(c.DSS,{}),y("google_login")]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:y("or")})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-black-300 rounded-lg text-black-700 hover:bg-black-50 transition-colors",onClick:()=>m("email"),children:[(0,a.jsx)(c.maD,{}),y("email_login")]})]}),"email"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:y("email_instruction")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("email_address")}),(0,a.jsx)("input",{type:"email",value:u,onChange:e=>x(e.target.value),placeholder:y("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&_(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),f&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:_,disabled:b,children:b?y("sending"):y("send_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:y("back")})]})]}),"code"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:y("verification_instruction",{email:u})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("verification_code")}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:t=>k(e,t.target.value),disabled:b,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("email"),children:y("resend_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:y("back")})]})]})]})]})]}):null}},8493:(e,t,s)=>{Promise.resolve().then(s.bind(s,6096)),Promise.resolve().then(s.bind(s,2342))},9651:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,iu:()=>r,ng:()=>i});var a=s(5155);function l(e){let{src:t,alt:s,width:l,height:r,className:i="",priority:n=!1,fill:o=!1,sizes:c,placeholder:d="empty",blurDataURL:m,fallbackSrc:u="/images/placeholder.svg"}=e;return o?(0,a.jsx)("div",{className:"relative overflow-hidden",children:(0,a.jsx)("img",{src:t,alt:s,className:i,style:{objectFit:"contain",padding:2,width:"100%",height:"100%"}})}):(0,a.jsx)("img",{src:t,alt:s,width:l,height:r,className:i,style:{objectFit:"contain",padding:2}})}s(2115);let r={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:84,height:84},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},i={avatar:"40px",toolLogo:"52px",toolLogoLarge:"84px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}}}]);