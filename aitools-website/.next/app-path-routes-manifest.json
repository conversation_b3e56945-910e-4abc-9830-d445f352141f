{"/_not-found/page": "/_not-found", "/api/admin/stats/route": "/api/admin/stats", "/api/admin/tools/[id]/approve/route": "/api/admin/tools/[id]/approve", "/api/admin/tools/[id]/reject/route": "/api/admin/tools/[id]/reject", "/api/admin/tools/route": "/api/admin/tools", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/me/route": "/api/auth/me", "/api/auth/send-code/route": "/api/auth/send-code", "/api/categories/route": "/api/categories", "/api/orders/[id]/pay/route": "/api/orders/[id]/pay", "/api/orders/[id]/route": "/api/orders/[id]", "/api/stripe/create-payment-intent/route": "/api/stripe/create-payment-intent", "/api/stripe/webhook/route": "/api/stripe/webhook", "/api/test/create-payment-intent/route": "/api/test/create-payment-intent", "/api/tools/[id]/comments/route": "/api/tools/[id]/comments", "/api/tools/[id]/launch-date/route": "/api/tools/[id]/launch-date", "/api/tools/[id]/like/route": "/api/tools/[id]/like", "/api/tools/[id]/route": "/api/tools/[id]", "/api/tools/route": "/api/tools", "/api/tools/publish/route": "/api/tools/publish", "/api/tools/submit/route": "/api/tools/submit", "/api/upload/avatar/route": "/api/upload/avatar", "/api/upload/logo/route": "/api/upload/logo", "/api/uploads/[...path]/route": "/api/uploads/[...path]", "/api/user/liked-tools/route": "/api/user/liked-tools", "/api/user/tools/route": "/api/user/tools", "/sitemap.xml/route": "/sitemap.xml", "/favicon.ico/route": "/favicon.ico", "/page": "/", "/[locale]/admin/dashboard/page": "/[locale]/admin/dashboard", "/[locale]/admin/tools/[id]/page": "/[locale]/admin/tools/[id]", "/[locale]/categories/[slug]/page": "/[locale]/categories/[slug]", "/[locale]/demo-features/page": "/[locale]/demo-features", "/[locale]/payment/checkout/page": "/[locale]/payment/checkout", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/profile/liked/page": "/[locale]/profile/liked", "/[locale]/profile/submitted/page": "/[locale]/profile/submitted", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/submit/launch-date-success/page": "/[locale]/submit/launch-date-success", "/[locale]/search/page": "/[locale]/search", "/[locale]/submit/page": "/[locale]/submit", "/[locale]/submit/tool-info-success/page": "/[locale]/submit/tool-info-success", "/[locale]/submit/success/page": "/[locale]/submit/success", "/[locale]/test-auth/page": "/[locale]/test-auth", "/[locale]/submit/launch-date/[toolId]/page": "/[locale]/submit/launch-date/[toolId]", "/[locale]/test-pricing/page": "/[locale]/test-pricing", "/[locale]/submit/edit/[toolId]/page": "/[locale]/submit/edit/[toolId]", "/[locale]/test-promotion/page": "/[locale]/test-promotion", "/[locale]/tools/[id]/page": "/[locale]/tools/[id]", "/[locale]/dashboard/page": "/[locale]/dashboard", "/[locale]/about/page": "/[locale]/about", "/[locale]/contact/page": "/[locale]/contact", "/[locale]/categories/page": "/[locale]/categories", "/[locale]/privacy/page": "/[locale]/privacy", "/[locale]/test-stripe/page": "/[locale]/test-stripe", "/[locale]/page": "/[locale]", "/[locale]/tools/page": "/[locale]/tools", "/[locale]/terms/page": "/[locale]/terms"}