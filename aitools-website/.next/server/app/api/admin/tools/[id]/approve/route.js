const CHUNK_PUBLIC_PATH = "server/app/api/admin/tools/[id]/approve/route.js";
const runtime = require("../../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_i18n_messages_bf7c7385._.js");
runtime.loadChunk("server/chunks/node_modules_0aa75c59._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__c20487b8._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/tools/[id]/approve/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/tools/[id]/approve/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/tools/[id]/approve/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
