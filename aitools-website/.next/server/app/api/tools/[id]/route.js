const CHUNK_PUBLIC_PATH = "server/app/api/tools/[id]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_i18n_messages_e9376c1c._.js");
runtime.loadChunk("server/chunks/node_modules_next_5579a25e._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_2070941b._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3be._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a80197._.js");
runtime.loadChunk("server/chunks/node_modules_dc7cbd77._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__584f19a7._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/tools/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/tools/[id]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/tools/[id]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
