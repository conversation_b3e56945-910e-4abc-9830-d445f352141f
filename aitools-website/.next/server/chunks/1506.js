exports.id=1506,exports.ids=[1506],exports.modules={595:(e,t,s)=>{Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.bind(s,45298))},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},18549:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>r,iu:()=>l,ng:()=>i});var a=s(60687);function r({src:e,alt:t,width:s,height:r,className:l="",priority:i=!1,fill:o=!1,sizes:n,placeholder:c="empty",blurDataURL:d,fallbackSrc:m="/images/placeholder.svg"}){return o?(0,a.jsx)("div",{className:"relative overflow-hidden",children:(0,a.jsx)("img",{src:e,alt:t,className:l,style:{objectFit:"contain",padding:2,width:"100%",height:"100%"}})}):(0,a.jsx)("img",{src:e,alt:t,width:s,height:r,className:l,style:{objectFit:"contain",padding:2}})}s(43210);let l={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:84,height:84},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},i={avatar:"40px",toolLogo:"52px",toolLogoLarge:"84px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},33823:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(60687);function r({size:e="md",className:t=""}){return(0,a.jsx)("div",{className:`flex justify-center items-center ${t}`,children:(0,a.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},37360:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},39636:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx","default")},45298:(e,t,s)=>{"use strict";s.d(t,{default:()=>E});var a=s(60687),r=s(43210),l=s(82136),i=s(12340),o=s(77618),n=s(33823),c=s(78890),d=s(48577),m=s(94865),u=s(28559),x=s(62688);let g=(0,x.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),h=(0,x.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),p=(0,x.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var f=s(11860),j=s(99270),y=s(37360);function N({selectedTags:e,onTagsChange:t,maxTags:s=3,placeholder:l}){let[n,c]=(0,r.useState)(""),[d,m]=(0,r.useState)(!1),u=(0,i.a8)(),x=(0,o.c3)("common"),g=function(){let e=(0,o.c3)("tags");return b.map(t=>({key:t,label:e(t)}))}();u?.startsWith("/en");let h=a=>{e.includes(a)?t(e.filter(e=>e!==a)):e.length<s&&t([...e,a])},p=s=>{t(e.filter(e=>e!==s))},N=g.filter(t=>t.label.toLowerCase().includes(n.toLowerCase())&&!e.includes(t.key)),v=e=>{let t=g.find(t=>t.key===e);return t?t.label:e};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:x("select_tags")}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:x("selected_count",{count:e.length,max:s})})]}),e.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:x("selected_tags")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[v(e),(0,a.jsx)("button",{type:"button",onClick:()=>p(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]},e))})]}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:x("select_tags_max",{max:s})}),(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)("input",{type:"text",placeholder:l||x("search_tags"),value:n,onChange:e=>c(e.target.value),onFocus:()=>m(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(j.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(d||n)&&(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:N.length>0?(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-1",children:N.map(t=>{let r=e.length>=s;return(0,a.jsx)("button",{type:"button",onClick:()=>{h(t.key),c(""),m(!1)},disabled:r,className:`
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${r?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700"}
                            `,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-2 text-gray-400"}),t.label]})},t.key)})}),N.length>50&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:x("found_tags",{count:N.length})})]}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:x(n?"no_tags_found":"start_typing")})})})]})}),(d||n)&&(0,a.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{m(!1),c("")}}),e.length>=s&&(0,a.jsx)("p",{className:"text-sm text-amber-600",children:x("max_tags_limit",{max:s})})]})}var v=s(18549);let w={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},WEBSITE_URL:{min:10,max:100,label:"Website URL"}};function _({current:e,max:t,min:s=0,className:r=""}){let l=e>t,i=e<s,o=e/t*100,n="text-gray-500";return n=l?"text-red-500":i?"text-orange-500":o>80?"text-yellow-600":"text-green-600",(0,a.jsxs)("div",{className:`text-sm ${n} ${r}`,children:[(0,a.jsx)("span",{className:"font-medium",children:e}),(0,a.jsxs)("span",{className:"text-gray-400",children:["/",t]}),s>0&&e<s&&(0,a.jsxs)("span",{className:"ml-2 text-orange-500",children:["(At least ",s," characters)"]}),l&&(0,a.jsxs)("span",{className:"ml-2 text-red-500",children:["(Exceeded by ",e-t," characters)"]})]})}function E({categoryOptions:e,isEditMode:t=!1,toolId:s,initialTool:x}){let b=(0,o.c3)("submit"),{data:f,status:j}=(0,l.useSession)(),y=(0,i.rd)(),[E,A]=(0,r.useState)(x||null),[k,F]=(0,r.useState)(t&&!x),[C,I]=(0,r.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[L,R]=(0,r.useState)(null),[S,T]=(0,r.useState)(""),[P,U]=(0,r.useState)(!1),[M,O]=(0,r.useState)(!1),[D,$]=(0,r.useState)("idle"),[q,B]=(0,r.useState)(""),[z,H]=(0,r.useState)({}),[W,Y]=(0,r.useState)(!1),G=e=>{let{name:t,value:s}=e.target;I(e=>({...e,[t]:s}))},Z=e=>{let t=e.target.files?.[0];if(t){I(e=>({...e,logoFile:t}));let e=new FileReader;e.onload=e=>{R(e.target?.result)},e.readAsDataURL(t)}},J=()=>{let e={};return C.name.trim()||(e.name=b("form.tool_name")+" is required"),C.description.trim()||(e.description=b("form.description")+" is required"),C.website.trim()||(e.website=b("form.website_url")+" is required"),C.category||(e.category=b("form.category")+" is required"),C.pricing||(e.pricing=b("form.pricing_model")+" is required"),C.website&&!C.website.match(/^https?:\/\/.+/)&&(e.website=b("form.website_url_placeholder")),t||C.logoFile||(e.logo=b("form.logo_required")),0===C.tags.length&&(e.tags=b("form.tags_placeholder")),H(e),0===Object.keys(e).length},V=async e=>{if(e.preventDefault(),!f?.user?.email)return void Y(!0);if(J()){O(!0),$("idle");try{let e=S;if(C.logoFile){let t=new FormData;t.append("logo",C.logoFile);let s=await fetch("/api/upload/logo",{method:"POST",body:t});if(s.ok)e=(await s.json()).data.url;else{let e=await s.json();throw Error(e.message||"Logo upload failed")}}if(t&&s){let t={name:C.name,tagline:C.tagline,description:C.description,website:C.website,logo:e||void 0,category:C.category,tags:C.tags,pricing:C.pricing},a=await fetch(`/api/tools/${s}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),r=await a.json();r.success?($("success"),B("工具信息更新成功！"),setTimeout(()=>{y.push("/profile/submitted")},2e3)):($("error"),B(r.error||"Update failed, please retry"))}else{let t={name:C.name,tagline:C.tagline,description:C.description,website:C.website,logo:e,category:C.category,tags:C.tags,pricing:C.pricing},s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(s.ok){let e=await s.json();$("success"),B(b("form.success_message")),setTimeout(()=>{y.push(`/submit/tool-info-success?toolId=${e.data.toolId}`)},500)}else{let e=await s.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),$("error"),B(e.message+". "+b("form.error_message"))}finally{O(!1)}}};return"loading"===j||k?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(n.A,{size:"lg"})}):f?t&&!E?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,a.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===D?(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)(c.A,{message:q||b("form.success_message")})}):(0,a.jsxs)(r.Fragment,{children:[(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)(i.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!t&&(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(g,{className:"h-8 w-8 text-blue-600"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:b("title")}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:b("subtitle")})]}),(0,a.jsxs)("form",{onSubmit:V,className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,a.jsx)(h,{className:"h-5 w-5 mr-2 text-blue-600"}),b("form.basic_info")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.tool_name")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:C.name,onChange:G,placeholder:b("form.tool_name_placeholder"),maxLength:w.TOOL_NAME.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(_,{current:C.name.length,max:w.TOOL_NAME.max,min:w.TOOL_NAME.min}),z.name&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:z.name})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.tagline")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:C.tagline,onChange:G,placeholder:b("form.tagline_placeholder"),maxLength:w.TAGLINE.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(_,{current:C.tagline.length,max:w.TAGLINE.max,min:w.TAGLINE.min}),z.tagline&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:z.tagline})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.description")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("textarea",{id:"description",name:"description",value:C.description,onChange:G,placeholder:b("form.description_placeholder"),maxLength:w.DESCRIPTION.max,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(_,{current:C.description.length,max:w.DESCRIPTION.max,min:w.DESCRIPTION.min}),z.description&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:z.description})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.website_url")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(p,{className:"h-5 w-5 text-gray-400"})}),!t&&(0,a.jsx)("input",{type:"url",id:"website",name:"website",value:C.website,onChange:G,placeholder:t?"https://example.com":b("form.website_url_placeholder"),maxLength:w.WEBSITE_URL.max,className:`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${z.website?"border-red-300":"border-gray-300"}`,required:!0}),t&&(0,a.jsx)("div",{className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:C.website})]}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[!t&&(0,a.jsx)(_,{current:C.website.length,max:w.WEBSITE_URL.max,min:w.WEBSITE_URL.min}),z.website&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:z.website})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.logo_upload")," ",!t&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{e.target.files?.[0]&&Z(e)},className:"hidden",id:"logo-upload",required:!t}),(0,a.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,a.jsx)(g,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:P?b("form.uploading"):b("form.click_to_upload")}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:b("form.logo_upload_hint")})]})]}),z.logo&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.logo})]}),L&&(0,a.jsx)(v.Ay,{alt:"app logo",src:L,width:v.iu.toolLogo.width,height:v.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:v.ng.toolLogo,placeholder:"blur"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:b("form.category_and_pricing")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.category")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("select",{id:"category",name:"category",value:C.category,onChange:G,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:b("form.category_placeholder")}),e.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.pricing_model")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("select",{id:"pricing",name:"pricing",value:C.pricing,onChange:G,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${z.pricing?"border-red-300":"border-gray-300"}`,required:!0,children:[(0,a.jsx)("option",{value:"",children:b("form.pricing_placeholder")}),m.Y$.map(e=>(0,a.jsx)("option",{value:e.value,children:b(`form.${e.value}`)},e.value))]}),z.pricing&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.pricing})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[b("form.tags")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(N,{selectedTags:C.tags,onTagsChange:e=>{I(t=>({...t,tags:e}))},maxTags:3,placeholder:b("form.tags_placeholder")}),z.tags&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.tags})]})]}),!t&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:b("form.guidelines_title")}),(0,a.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_1")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_2")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_3")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_4")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),b("form.guideline_5")]})]})]}),(0,a.jsx)("div",{className:t?"flex justify-end":"flex justify-center",children:(0,a.jsx)("button",{type:"submit",disabled:M,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors px-8 py-3 text-base",children:M?(0,a.jsxs)(r.Fragment,{children:[(0,a.jsx)(n.A,{size:"sm",className:"mr-2"}),b("form.submitting")]}):(0,a.jsxs)(r.Fragment,{children:[(0,a.jsx)(g,{className:"h-5 w-5 mr-2"}),b("form.submit_button")]})})})]}),"error"===D&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-800",children:q})})]}),(0,a.jsx)(d.A,{isOpen:W,onClose:()=>Y(!1)})]}):(0,a.jsxs)(r.Fragment,{children:[(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:b("auth.login_required")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:b("auth.login_to_submit")}),(0,a.jsx)("button",{onClick:()=>Y(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:b("auth.login")})]})}),(0,a.jsx)(d.A,{isOpen:W,onClose:()=>Y(!1)})]})}process.env.UPLOAD_BASE_DIR},60366:(e,t,s)=>{"use strict";s.d(t,{BB:()=>i,PZ:()=>o,RI:()=>c,ut:()=>n});var a=s(64348);let r=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function l(e){let t=await (0,a.A)({locale:e||"en",namespace:"categories"});return r.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function i(e){return(await l(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,t){return(await l(t)).find(t=>t.slug===e)}let n=r.map(e=>e.slug),c=r.reduce((e,t)=>(e[t.slug]=t,e),{})},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:l="",children:i,iconNode:d,...m},u)=>(0,a.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:r?24*Number(s)/Number(t):s,className:o("lucide",l),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let s=(0,a.forwardRef)(({className:s,...l},n)=>(0,a.createElement)(d,{ref:n,iconNode:t,className:o(`lucide-${r(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},78890:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(60687),r=s(5336),l=s(11860);function i({message:e,onClose:t,className:s=""}){return(0,a.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${s}`,children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-green-800 text-sm",children:e})}),t&&(0,a.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}},89979:(e,t,s)=>{Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.bind(s,39636))},94865:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>n,S9:()=>m,Y$:()=>o,kX:()=>a,mV:()=>c,mp:()=>x,sT:()=>u,tF:()=>g,v4:()=>i,vS:()=>r});let a={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},r=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"All Prices"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],o=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],n=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,m=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=()=>a.PRIORITY_LAUNCH.promotion,x=()=>{let e=a.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},g=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};