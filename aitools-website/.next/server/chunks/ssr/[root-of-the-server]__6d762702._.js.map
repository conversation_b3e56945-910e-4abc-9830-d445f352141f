{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/tools/LikeButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/tools/LikeButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/tools/LikeButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/tools/LikeButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts) as any;\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAc;IAClD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IComment extends Document {\n  toolId: string;\n  userId: string;\n  content: string;\n  parentId?: string; // 支持回复评论\n  likes: number;\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst CommentSchema: Schema = new Schema({\n  toolId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Tool',\n    required: [true, 'Tool ID is required']\n  },\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'User ID is required']\n  },\n  content: {\n    type: String,\n    required: [true, 'Comment content is required'],\n    trim: true,\n    maxlength: [1000, 'Comment cannot exceed 1000 characters'],\n    minlength: [1, 'Comment cannot be empty']\n  },\n  parentId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Comment',\n    default: null\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nCommentSchema.index({ toolId: 1, createdAt: -1 });\nCommentSchema.index({ userId: 1 });\nCommentSchema.index({ parentId: 1 });\nCommentSchema.index({ isActive: 1 });\n\n// 虚拟字段 - 获取回复数量\nCommentSchema.virtual('replyCount', {\n  ref: 'Comment',\n  localField: '_id',\n  foreignField: 'parentId',\n  count: true,\n  match: { isActive: true }\n});\n\n// 静态方法 - 获取工具的评论（包含回复）\nCommentSchema.statics.getToolComments = function(toolId: string) {\n  return this.find({ \n    toolId, \n    isActive: true \n  })\n  .populate('userId', 'name avatar')\n  .populate({\n    path: 'parentId',\n    select: 'content userId',\n    populate: {\n      path: 'userId',\n      select: 'name'\n    }\n  })\n  .sort({ createdAt: -1 });\n};\n\n// 实例方法 - 获取回复\nCommentSchema.methods.getReplies = function() {\n  return mongoose.model('Comment').find({\n    parentId: this._id,\n    isActive: true\n  })\n  .populate('userId', 'name avatar')\n  .sort({ createdAt: 1 });\n};\n\nexport default mongoose.models.Comment || mongoose.model<IComment>('Comment', CommentSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAaA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA8B;QAC/C,MAAM;QACN,WAAW;YAAC;YAAM;SAAwC;QAC1D,WAAW;YAAC;YAAG;SAA0B;IAC3C;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC/C,cAAc,KAAK,CAAC;IAAE,QAAQ;AAAE;AAChC,cAAc,KAAK,CAAC;IAAE,UAAU;AAAE;AAClC,cAAc,KAAK,CAAC;IAAE,UAAU;AAAE;AAElC,gBAAgB;AAChB,cAAc,OAAO,CAAC,cAAc;IAClC,KAAK;IACL,YAAY;IACZ,cAAc;IACd,OAAO;IACP,OAAO;QAAE,UAAU;IAAK;AAC1B;AAEA,uBAAuB;AACvB,cAAc,OAAO,CAAC,eAAe,GAAG,SAAS,MAAc;IAC7D,OAAO,IAAI,CAAC,IAAI,CAAC;QACf;QACA,UAAU;IACZ,GACC,QAAQ,CAAC,UAAU,eACnB,QAAQ,CAAC;QACR,MAAM;QACN,QAAQ;QACR,UAAU;YACR,MAAM;YACN,QAAQ;QACV;IACF,GACC,IAAI,CAAC;QAAE,WAAW,CAAC;IAAE;AACxB;AAEA,cAAc;AACd,cAAc,OAAO,CAAC,UAAU,GAAG;IACjC,OAAO,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC;QACpC,UAAU,IAAI,CAAC,GAAG;QAClB,UAAU;IACZ,GACC,QAAQ,CAAC,UAAU,eACnB,IAAI,CAAC;QAAE,WAAW;IAAE;AACvB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/tools/CommentSectionClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/tools/CommentSectionClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/tools/CommentSectionClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/tools/CommentSectionClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionServer.tsx"], "sourcesContent": ["import dbConnect from '@/lib/mongodb';\nimport Comment from '@/models/Comment';\nimport CommentSectionClient from './CommentSectionClient';\n\ninterface CommentSectionServerProps {\n  toolId: string;\n  onLoginRequired?: () => void;\n}\n\ninterface CommentData {\n  _id: string;\n  content: string;\n  userId: {\n    _id: string;\n    name: string;\n    email: string;\n    image?: string;\n  };\n  createdAt: string;\n  likes: number;\n  replies?: CommentData[];\n}\n\nasync function getComments(toolId: string): Promise<CommentData[]> {\n  try {\n    await dbConnect();\n\n    // 查找评论并关联用户信息\n    const comments = await Comment.find({ \n      toolId, \n      isActive: true,\n      parentId: null // 只获取顶级评论\n    })\n    .populate('userId', 'name email image')\n    .sort({ createdAt: -1 })\n    .limit(10); // 限制初始加载的评论数量\n\n    // 获取每个评论的回复\n    const commentsWithReplies = await Promise.all(\n      comments.map(async (comment) => {\n        const replies = await Comment.find({\n          parentId: comment._id,\n          isActive: true\n        })\n        .populate('userId', 'name email image')\n        .sort({ createdAt: 1 });\n\n        return {\n          _id: comment._id.toString(),\n          content: comment.content,\n          userId: {\n            _id: comment.userId._id.toString(),\n            name: comment.userId.name,\n            email: comment.userId.email,\n            image: comment.userId.image\n          },\n          createdAt: comment.createdAt.toISOString(),\n          likes: comment.likes || 0,\n          replies: replies.map(reply => ({\n            _id: reply._id.toString(),\n            content: reply.content,\n            userId: {\n              _id: reply.userId._id.toString(),\n              name: reply.userId.name,\n              email: reply.userId.email,\n              image: reply.userId.image\n            },\n            createdAt: reply.createdAt.toISOString(),\n            likes: reply.likes || 0\n          }))\n        };\n      })\n    );\n\n    return commentsWithReplies;\n  } catch (error) {\n    console.error('Error fetching comments:', error);\n    return [];\n  }\n}\n\nexport default async function CommentSectionServer({\n  toolId,\n  onLoginRequired\n}: CommentSectionServerProps) {\n  const comments = await getComments(toolId);\n\n  return (\n    <CommentSectionClient\n      toolId={toolId}\n      initialComments={comments}\n      onLoginRequired={onLoginRequired}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAqBA,eAAe,YAAY,MAAc;IACvC,IAAI;QACF,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,cAAc;QACd,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YAClC;YACA,UAAU;YACV,UAAU,KAAK,UAAU;QAC3B,GACC,QAAQ,CAAC,UAAU,oBACnB,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,KAAK,CAAC,KAAK,cAAc;QAE1B,YAAY;QACZ,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,SAAS,GAAG,CAAC,OAAO;YAClB,MAAM,UAAU,MAAM,wHAAA,CAAA,UAAO,CAAC,IAAI,CAAC;gBACjC,UAAU,QAAQ,GAAG;gBACrB,UAAU;YACZ,GACC,QAAQ,CAAC,UAAU,oBACnB,IAAI,CAAC;gBAAE,WAAW;YAAE;YAErB,OAAO;gBACL,KAAK,QAAQ,GAAG,CAAC,QAAQ;gBACzB,SAAS,QAAQ,OAAO;gBACxB,QAAQ;oBACN,KAAK,QAAQ,MAAM,CAAC,GAAG,CAAC,QAAQ;oBAChC,MAAM,QAAQ,MAAM,CAAC,IAAI;oBACzB,OAAO,QAAQ,MAAM,CAAC,KAAK;oBAC3B,OAAO,QAAQ,MAAM,CAAC,KAAK;gBAC7B;gBACA,WAAW,QAAQ,SAAS,CAAC,WAAW;gBACxC,OAAO,QAAQ,KAAK,IAAI;gBACxB,SAAS,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;wBAC7B,KAAK,MAAM,GAAG,CAAC,QAAQ;wBACvB,SAAS,MAAM,OAAO;wBACtB,QAAQ;4BACN,KAAK,MAAM,MAAM,CAAC,GAAG,CAAC,QAAQ;4BAC9B,MAAM,MAAM,MAAM,CAAC,IAAI;4BACvB,OAAO,MAAM,MAAM,CAAC,KAAK;4BACzB,OAAO,MAAM,MAAM,CAAC,KAAK;wBAC3B;wBACA,WAAW,MAAM,SAAS,CAAC,WAAW;wBACtC,OAAO,MAAM,KAAK,IAAI;oBACxB,CAAC;YACH;QACF;QAGF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,EAAE;IACX;AACF;AAEe,eAAe,qBAAqB,EACjD,MAAM,EACN,eAAe,EACW;IAC1B,MAAM,WAAW,MAAM,YAAY;IAEnC,qBACE,8OAAC,mJAAA,CAAA,UAAoB;QACnB,QAAQ;QACR,iBAAiB;QACjB,iBAAiB;;;;;;AAGvB", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/tools/ToolDetailInteraction.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/tools/ToolDetailInteraction.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/tools/ToolDetailInteraction.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/tools/ToolDetailInteraction.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // 构建时特殊处理：如果是构建阶段，使用固定的URL\n    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL && !process.env.NETLIFY) {\n      // 生产构建时，假设服务器将在3011端口运行\n      return 'http://localhost:3011';\n    }\n\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n\n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n\n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n\n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n\n    // 开发环境默认值\n    const port = process.env.PORT || '3011';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3011';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3011';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 获取AI服务URL配置\n */\nexport function getAiServiceUrls() {\n  return {\n    generateProductInfo: process.env.AI_GENERATE_PRODUCT_INFO_URL || 'http://localhost:50058/ai/generateProductInfo',\n  };\n}\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n  console.log('  AI Service URLs:', getAiServiceUrls());\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,2BAA2B;QAC3B,uCAA8F;;QAG9F;QAEA,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAKO,SAAS;IACd,OAAO;QACL,qBAAqB,QAAQ,GAAG,CAAC,4BAA4B,IAAI;IACnE;AACF;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;IACtC,QAAQ,GAAG,CAAC,sBAAsB;AACpC", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      console.log('API request:', { url, config });\n      \n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n\n  // 订单API\n  async getOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}`);\n  }\n\n  async processOrderPayment(id: string, data: {\n    paymentMethod?: string;\n  }): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}/pay`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  // 用户API\n  async getCurrentUser(): Promise<ApiResponse<any>> {\n    return this.request<any>('/auth/me');\n  }\n\n  async updateProfile(data: {\n    name?: string;\n    avatar?: string;\n    bio?: string;\n    website?: string;\n    location?: string;\n  }): Promise<ApiResponse<any>> {\n    return this.request<any>('/auth/me', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    });\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,QAAQ,GAAG,CAAC,gBAAgB;gBAAE;gBAAK;YAAO;YAE1C,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;IAEA,QAAQ;IACR,MAAM,SAAS,EAAU,EAA6B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,IAAI;IAC1C;IAEA,MAAM,oBAAoB,EAAU,EAAE,IAErC,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ;IACR,MAAM,iBAA4C;QAChD,OAAO,IAAI,CAAC,OAAO,CAAM;IAC3B;IAEA,MAAM,cAAc,IAMnB,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAM,YAAY;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // Priority launch service pricing\n  PRIORITY_LAUNCH: {\n    // Display price (CNY)\n    displayPrice: 19.9,\n    // Original price (CNY) - for showing pre-discount price\n    originalPrice: 49.9,\n    // Stripe price (in cents)\n    stripeAmount: 1990,\n    // Original Stripe amount (in cents)\n    originalStripeAmount: 4990,\n    // Currency\n    currency: 'USD',\n    // Stripe currency code (lowercase)\n    stripeCurrency: 'usd', // Note: currently using USD for testing\n    // Product name\n    productName: 'AI Tool Priority Launch Service',\n    // Product description\n    description: 'Get your AI tool prioritized for review and featured placement',\n    // Limited-time promotion info\n    promotion: {\n      // Whether the promotion is enabled\n      enabled: true,\n      // Promotion description\n      description: 'Limited-time offer - First 100 paid users',\n      // Discount percentage\n      discountPercent: 50,\n      // Remaining slots (can be dynamically fetched from database)\n      remainingSlots: 85\n    },\n    // Feature list\n    features: [\n      'Choose any publish date',\n      'Priority review processing',\n      'Featured homepage placement',\n      'Dedicated customer support'\n    ]\n  },\n\n  // Free launch configuration\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: 'Free Launch Service',\n    description: 'Choose any publish date after one month',\n    features: [\n      'Free submission for review',\n      'Publish date: from one month later',\n      'Standard review process',\n      'Standard display placement'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: 'Free',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: ' Freemium',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: 'Paid',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: 'All Prices' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化原价显示（带删除线）\nexport const formatOriginalPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 获取促销信息\nexport const getPromotionInfo = () => {\n  return PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n};\n\n// 检查是否有促销活动\nexport const hasActivePromotion = () => {\n  const promotion = PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n  return promotion.enabled && promotion.remainingSlots > 0;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,iBAAiB;QACf,sBAAsB;QACtB,cAAc;QACd,wDAAwD;QACxD,eAAe;QACf,0BAA0B;QAC1B,cAAc;QACd,oCAAoC;QACpC,sBAAsB;QACtB,WAAW;QACX,UAAU;QACV,mCAAmC;QACnC,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,sBAAsB;QACtB,aAAa;QACb,8BAA8B;QAC9B,WAAW;YACT,mCAAmC;YACnC,SAAS;YACT,wBAAwB;YACxB,aAAa;YACb,sBAAsB;YACtB,iBAAiB;YACjB,6DAA6D;YAC7D,gBAAgB;QAClB;QACA,eAAe;QACf,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,4BAA4B;IAC5B,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,sBAAsB,CAAC,OAAe;IACjD,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,mBAAmB;IAC9B,OAAO,eAAe,eAAe,CAAC,SAAS;AACjD;AAGO,MAAM,qBAAqB;IAChC,MAAM,YAAY,eAAe,eAAe,CAAC,SAAS;IAC1D,OAAO,UAAU,OAAO,IAAI,UAAU,cAAc,GAAG;AACzD;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n}\n\n// 生成模糊占位符数据URL\nfunction generateBlurDataURL(): string {\n  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==';\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n}: OptimizedImageProps) {\n  const imageProps = {\n    src,\n    alt,\n    className,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <img\n          src={src}\n          alt={alt}\n          className={className}\n          style={{ objectFit: 'contain', padding: 2, width: '100%', height: '100%' }}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={src}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      style={{ objectFit: 'contain', padding: 2 }}\n    />\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 52, height: 52 },\n  toolLogoLarge: { width: 84, height: 84 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '52px',\n  toolLogoLarge: '84px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;;AAiBA,eAAe;AACf,SAAS;IACP,OAAO;AACT;AAEe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACnB;IACpB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAIA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;oBAAE,WAAW;oBAAW,SAAS;oBAAG,OAAO;oBAAQ,QAAQ;gBAAO;;;;;;;;;;;IAIjF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,OAAO;YAAE,WAAW;YAAW,SAAS;QAAE;;;;;;AAGhD;AAGO,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAI,QAAQ;IAAG;IACvC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,eAAe;IACf,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolLogo.tsx"], "sourcesContent": ["import { Tool } from \"@/lib/api\";\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from \"./ui/OptimizedImage\";\n\n\nconst ToolLogo = ({ tool, size }: { tool: any, size: any }) => {\n    if (!tool.logo) {\n        return <div \n            className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\"\n        >\n            <span className=\"text-white font-bold text-lg\">\n                {tool.name.charAt(0).toUpperCase()}\n            </span>\n        </div>\n    } else {\n        return <OptimizedImage\n            src={tool.logo}\n            alt={`${tool.name} logo`}\n            width={size==='lg' ? ImageSizes.toolLogoLarge.width : ImageSizes.toolLogo.width}\n            height={size==='lg' ? ImageSizes.toolLogoLarge.height : ImageSizes.toolLogo.height}\n            className=\"rounded-lg object-contain flex-shrink-0\"\n            sizes={size==='lg' ? ResponsiveSizes.toolLogoLarge : ResponsiveSizes.toolLogo}\n            placeholder=\"blur\"\n        />\n    }\n};\n\nexport default ToolLogo;"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAA4B;IACtD,IAAI,CAAC,KAAK,IAAI,EAAE;QACZ,qBAAO,8OAAC;YACJ,WAAU;sBAEV,cAAA,8OAAC;gBAAK,WAAU;0BACX,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;IAG5C,OAAO;QACH,qBAAO,8OAAC,0IAAA,CAAA,UAAc;YAClB,KAAK,KAAK,IAAI;YACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;YACxB,OAAO,SAAO,OAAO,0IAAA,CAAA,aAAU,CAAC,aAAa,CAAC,KAAK,GAAG,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;YAC/E,QAAQ,SAAO,OAAO,0IAAA,CAAA,aAAU,CAAC,aAAa,CAAC,MAAM,GAAG,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;YAClF,WAAU;YACV,OAAO,SAAO,OAAO,0IAAA,CAAA,kBAAe,CAAC,aAAa,GAAG,0IAAA,CAAA,kBAAe,CAAC,QAAQ;YAC7E,aAAY;;;;;;IAEpB;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ServerMarkdownRenderer.tsx"], "sourcesContent": ["import { remark } from 'remark';\nimport html from 'remark-html';\nimport remarkGfm from 'remark-gfm';\n\ninterface MarkdownRendererProps {\n  content: string;\n  className?: string;\n}\n\nexport default async function ServerMarkdownRenderer({\n  content,\n  className = '',\n}: MarkdownRendererProps) {\n  const processedContent = await remark()\n    .use(remarkGfm)\n    .use(html, { sanitize: false }) // 注意这里不要开启 sanitize，保留 GFM 输出\n    .process(content);\n\n  const contentHtml = processedContent.toString();\n\n  return (\n    <div\n      className={`prose prose-slate max-w-none ${className}`}\n      style={{\n        listStylePosition: 'inside',\n        paddingLeft: '1.25rem',\n      }}\n      dangerouslySetInnerHTML={{ __html: contentHtml }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAOe,eAAe,uBAAuB,EACnD,OAAO,EACP,YAAY,EAAE,EACQ;IACtB,MAAM,mBAAmB,MAAM,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,IACjC,GAAG,CAAC,6IAAA,CAAA,UAAS,EACb,GAAG,CAAC,8IAAA,CAAA,UAAI,EAAE;QAAE,UAAU;IAAM,GAAG,8BAA8B;KAC7D,OAAO,CAAC;IAEX,MAAM,cAAc,iBAAiB,QAAQ;IAE7C,qBACE,8OAAC;QACC,WAAW,CAAC,6BAA6B,EAAE,WAAW;QACtD,OAAO;YACL,mBAAmB;YACnB,aAAa;QACf;QACA,yBAAyB;YAAE,QAAQ;QAAY;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailServer.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from '@/i18n/routing';\nimport { getTranslations } from 'next-intl/server';\nimport LikeButton from '@/components/tools/LikeButton';\nimport CommentSectionServer from '@/components/tools/CommentSectionServer';\nimport ToolDetailInteraction from '@/components/tools/ToolDetailInteraction';\nimport { apiClient, Tool } from '@/lib/api';\nimport { getToolPricingColor, getToolPricingText } from '@/constants/pricing';\nimport {\n  ExternalLink,\n  Heart,\n  Eye,\n  Tag,\n  DollarSign,\n  Share2\n} from 'lucide-react';\nimport { Locale } from '@/i18n/config';\nimport Tool<PERSON>ogo from '../ToolLogo';\nimport ServerMarkdownRenderer from '../ServerMarkdownRenderer';\n\ninterface ToolDetailServerProps {\n  initialTool: Tool;\n  toolId: string;\n  locale: Locale;\n}\n\n// 获取相关工具\nasync function getRelatedTools(category: string, currentToolId: string): Promise<Tool[]> {\n  try {\n    const response = await apiClient.getTools({\n      category,\n      limit: 4,\n      status: 'approved'\n    });\n    \n    if (response.success && response.data) {\n      // 过滤掉当前工具\n      return response.data.tools.filter(tool => tool._id !== currentToolId);\n    }\n    return [];\n  } catch (error) {\n    console.error('Error fetching related tools:', error);\n    return [];\n  }\n}\n\nexport default async function ToolDetailServer({ \n  initialTool, \n  toolId, \n  locale \n}: ToolDetailServerProps) {\n  const t = await getTranslations({ locale, namespace: 'tool_detail' });\n  const tTags = await getTranslations({ locale, namespace: 'tags' });\n\n  const relatedTools = await getRelatedTools(initialTool.category, toolId);\n\n  return (\n    <ToolDetailInteraction>\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Main Content */}\n        <div className=\"lg:col-span-2\">\n          {/* Tool Header */}\n          <article className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n            <header className=\"flex items-start justify-between mb-6\">\n              <div className=\"flex items-center space-x-4\">\n                {<ToolLogo tool={initialTool} size=\"sm\" />}\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                    {initialTool.name}\n                  </h1>\n                  {initialTool.tagline && (\n                    <p className=\"text-gray-600 mb-2\">\n                      {initialTool.tagline}\n                    </p>\n                  )}\n                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getToolPricingColor(initialTool.pricing)}`}>\n                    {getToolPricingText(initialTool.pricing)}\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <LikeButton\n                  toolId={toolId}\n                  size=\"lg\"\n                />\n                <a\n                  href={initialTool.website}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <ExternalLink className=\"mr-2 h-4 w-4\" />\n                  {t('visit_website')}\n                </a>\n              </div>\n            </header>\n\n            {/* Tool Description */}\n            <div className=\"prose max-w-none markdown-body\">\n              {/* <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {t('description')}\n              </h2> */}\n              <ServerMarkdownRenderer\n                content={initialTool.description}\n                // className=\"text-gray-600 text-sm leading-relaxed\"\n              />\n            </div>\n\n            {/* Tool Tags */}\n            {initialTool.tags && initialTool.tags.length > 0 && (\n              <div className=\"mt-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\n                  <Tag className=\"mr-2 h-5 w-5\" />\n                  {t('tags')}\n                </h3>\n                <div className=\"flex flex-wrap gap-2\">\n                  {initialTool.tags.map((tag, index) => (\n                    <span\n                      key={index}\n                      className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800\"\n                    >\n                      {tTags(tag)}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Tool Stats */}\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"flex items-center space-x-6\">\n                <div className=\"flex items-center text-gray-600\">\n                  <Eye className=\"mr-2 h-5 w-5\" />\n                  <span>{initialTool.views || 0} {t('views')}</span>\n                </div>\n                <div className=\"flex items-center text-gray-600\">\n                  <Heart className=\"mr-2 h-5 w-5\" />\n                  <span>{initialTool.likes || 0} {t('likes')}</span>\n                </div>\n              </div>\n            </div>\n          </article>\n\n          {/* Comments Section */}\n          <CommentSectionServer toolId={toolId} />\n        </div>\n\n        {/* Sidebar */}\n        <aside className=\"lg:col-span-1\">\n          {/* Tool Info */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('tool_info')}</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-gray-600\">{t('category')}</span>\n                <span className=\"text-gray-900 font-medium\">{initialTool.category}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-gray-600\">{t('pricing_model')}</span>\n                <span className={`px-2 py-1 rounded text-sm font-medium ${getToolPricingColor(initialTool.pricing)}`}>\n                  {getToolPricingText(initialTool.pricing)}\n                </span>\n              </div>\n              {initialTool.launchDate && (\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">{t('launch_date')}</span>\n                  <span className=\"text-gray-900 font-medium\">\n                    {new Date(initialTool.launchDate).toLocaleDateString()}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Related Tools */}\n          {relatedTools.length > 0 && (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('related_tools')}</h3>\n              <div className=\"space-y-4\">\n                {relatedTools.map((tool) => (\n                  <Link\n                    key={tool._id}\n                    href={`/tools/${tool._id}`}\n                    className=\"block p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all\"\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      {tool.logo ? (\n                        <img\n                          src={tool.logo}\n                          alt={`${tool.name} logo`}\n                          className=\"w-10 h-10 rounded object-cover\"\n                        />\n                      ) : (\n                        <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center\">\n                          <span className=\"text-white font-bold text-sm\">\n                            {tool.name.charAt(0).toUpperCase()}\n                          </span>\n                        </div>\n                      )}\n                      <div className=\"flex-1 min-w-0\">\n                        <h4 className=\"text-sm font-medium text-gray-900 truncate\">\n                          {tool.name}\n                        </h4>\n                        <p className=\"text-xs text-gray-500 truncate\">\n                          {tool.tagline}\n                        </p>\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            </div>\n          )}\n        </aside>\n      </div>\n    </ToolDetailInteraction>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AASA;AACA;;;;;;;;;;;;AAQA,SAAS;AACT,eAAe,gBAAgB,QAAgB,EAAE,aAAqB;IACpE,IAAI;QACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YACxC;YACA,OAAO;YACP,QAAQ;QACV;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,UAAU;YACV,OAAO,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;QACzD;QACA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEe,eAAe,iBAAiB,EAC7C,WAAW,EACX,MAAM,EACN,MAAM,EACgB;IACtB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAc;IACnE,MAAM,QAAQ,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAO;IAEhE,MAAM,eAAe,MAAM,gBAAgB,YAAY,QAAQ,EAAE;IAEjE,qBACE,8OAAC,oJAAA,CAAA,UAAqB;kBACpB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;;8DACZ,8OAAC,8HAAA,CAAA,UAAQ;oDAAC,MAAM;oDAAa,MAAK;;;;;;8DACnC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,YAAY,IAAI;;;;;;wDAElB,YAAY,OAAO,kBAClB,8OAAC;4DAAE,WAAU;sEACV,YAAY,OAAO;;;;;;sEAGxB,8OAAC;4DAAK,WAAW,CAAC,oEAAoE,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,OAAO,GAAG;sEAC/H,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,OAAO;;;;;;;;;;;;;;;;;;sDAK7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,yIAAA,CAAA,UAAU;oDACT,QAAQ;oDACR,MAAK;;;;;;8DAEP,8OAAC;oDACC,MAAM,YAAY,OAAO;oDACzB,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDACvB,EAAE;;;;;;;;;;;;;;;;;;;8CAMT,8OAAC;oCAAI,WAAU;8CAIb,cAAA,8OAAC,4IAAA,CAAA,UAAsB;wCACrB,SAAS,YAAY,WAAW;;;;;;;;;;;gCAMnC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG,mBAC7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd,EAAE;;;;;;;sDAEL,8OAAC;4CAAI,WAAU;sDACZ,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC1B,8OAAC;oDAEC,WAAU;8DAET,MAAM;mDAHF;;;;;;;;;;;;;;;;8CAWf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;;4DAAM,YAAY,KAAK,IAAI;4DAAE;4DAAE,EAAE;;;;;;;;;;;;;0DAEpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAM,YAAY,KAAK,IAAI;4DAAE;4DAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO1C,8OAAC,mJAAA,CAAA,UAAoB;4BAAC,QAAQ;;;;;;;;;;;;8BAIhC,8OAAC;oBAAM,WAAU;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C,EAAE;;;;;;8CAC5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB,EAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DAA6B,YAAY,QAAQ;;;;;;;;;;;;sDAEnE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB,EAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,CAAC,sCAAsC,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,OAAO,GAAG;8DACjG,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,OAAO;;;;;;;;;;;;wCAG1C,YAAY,UAAU,kBACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB,EAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;wBAQ7D,aAAa,MAAM,GAAG,mBACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C,EAAE;;;;;;8CAC5D,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,sHAAA,CAAA,OAAI;4CAEH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;4CAC1B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,iBACR,8OAAC;wDACC,KAAK,KAAK,IAAI;wDACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;wDACxB,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;kEAItC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,KAAK,IAAI;;;;;;0EAEZ,8OAAC;gEAAE,WAAU;0EACV,KAAK,OAAO;;;;;;;;;;;;;;;;;;2CAvBd,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCjC", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts"], "sourcesContent": ["import { Tool } from '@/lib/api';\n\nconst baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';\n\n// 网站基础结构化数据\nexport function getWebsiteStructuredData() {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    \"name\": \"AI工具导航\",\n    \"description\": \"发现最好的AI工具，提升您的工作效率和创造力\",\n    \"url\": baseUrl,\n    \"potentialAction\": {\n      \"@type\": \"SearchAction\",\n      \"target\": {\n        \"@type\": \"EntryPoint\",\n        \"urlTemplate\": `${baseUrl}/tools?search={search_term_string}`\n      },\n      \"query-input\": \"required name=search_term_string\"\n    },\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"url\": baseUrl\n    }\n  };\n}\n\n// 组织结构化数据\nexport function getOrganizationStructuredData() {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"AI工具导航\",\n    \"description\": \"专业的AI工具发现和推荐平台\",\n    \"url\": baseUrl,\n    \"logo\": `${baseUrl}/logo.png`,\n    \"sameAs\": [\n      // 可以添加社交媒体链接\n    ]\n  };\n}\n\n// 工具详情页结构化数据\nexport function getToolStructuredData(tool: Tool) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"SoftwareApplication\",\n    \"name\": tool.name,\n    \"description\": tool.description,\n    \"url\": tool.website,\n    \"applicationCategory\": \"AI工具\",\n    \"operatingSystem\": \"Web\",\n    \"offers\": {\n      \"@type\": \"Offer\",\n      \"price\": tool.pricing === 'free' ? \"0\" : undefined,\n      \"priceCurrency\": \"USD\",\n      \"availability\": \"https://schema.org/InStock\"\n    },\n    \"aggregateRating\": tool.likes ? {\n      \"@type\": \"AggregateRating\",\n      \"ratingValue\": Math.min(5, Math.max(1, (tool.likes / 10) + 3)), // 简单的评分算法\n      \"reviewCount\": tool.likes,\n      \"bestRating\": 5,\n      \"worstRating\": 1\n    } : undefined,\n    \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`,\n    \"datePublished\": tool.launchDate,\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"url\": baseUrl\n    }\n  };\n}\n\n// 面包屑导航结构化数据\nexport function getBreadcrumbStructuredData(items: Array<{name: string, url: string}>) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    \"itemListElement\": items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"name\": item.name,\n      \"item\": `${baseUrl}${item.url}`\n    }))\n  };\n}\n\n// 工具列表页结构化数据\nexport function getToolListStructuredData(tools: Tool[], category?: string) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ItemList\",\n    \"name\": category ? `${category} AI工具` : \"AI工具列表\",\n    \"description\": category ? `发现最好的${category} AI工具` : \"发现最好的AI工具\",\n    \"numberOfItems\": tools.length,\n    \"itemListElement\": tools.map((tool, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"item\": {\n        \"@type\": \"SoftwareApplication\",\n        \"name\": tool.name,\n        \"description\": tool.description,\n        \"url\": `${baseUrl}/tools/${tool._id}`,\n        \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`\n      }\n    }))\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AAG7C,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,mBAAmB;YACjB,SAAS;YACT,UAAU;gBACR,SAAS;gBACT,eAAe,GAAG,QAAQ,kCAAkC,CAAC;YAC/D;YACA,eAAe;QACjB;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAGO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,QAAQ,GAAG,QAAQ,SAAS,CAAC;QAC7B,UAAU,EAET;IACH;AACF;AAGO,SAAS,sBAAsB,IAAU;IAC9C,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ,KAAK,IAAI;QACjB,eAAe,KAAK,WAAW;QAC/B,OAAO,KAAK,OAAO;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,UAAU;YACR,SAAS;YACT,SAAS,KAAK,OAAO,KAAK,SAAS,MAAM;YACzC,iBAAiB;YACjB,gBAAgB;QAClB;QACA,mBAAmB,KAAK,KAAK,GAAG;YAC9B,SAAS;YACT,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,AAAC,KAAK,KAAK,GAAG,KAAM;YAC3D,eAAe,KAAK,KAAK;YACzB,cAAc;YACd,eAAe;QACjB,IAAI;QACJ,SAAS,KAAK,IAAI,IAAI,GAAG,QAAQ,uBAAuB,CAAC;QACzD,iBAAiB,KAAK,UAAU;QAChC,aAAa;YACX,SAAS;YACT,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAGO,SAAS,4BAA4B,KAAyC;IACnF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,GAAG,UAAU,KAAK,GAAG,EAAE;YACjC,CAAC;IACH;AACF;AAGO,SAAS,0BAA0B,KAAa,EAAE,QAAiB;IACxE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ,WAAW,GAAG,SAAS,KAAK,CAAC,GAAG;QACxC,eAAe,WAAW,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,GAAG;QACpD,iBAAiB,MAAM,MAAM;QAC7B,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ;oBACN,SAAS;oBACT,QAAQ,KAAK,IAAI;oBACjB,eAAe,KAAK,WAAW;oBAC/B,OAAO,GAAG,QAAQ,OAAO,EAAE,KAAK,GAAG,EAAE;oBACrC,SAAS,KAAK,IAAI,IAAI,GAAG,QAAQ,uBAAuB,CAAC;gBAC3D;YACF,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 1754, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/tools/%5Bid%5D/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from '@/i18n/routing';\nimport { Metadata } from 'next';\nimport { notFound } from 'next/navigation';\nimport { getTranslations } from 'next-intl/server';\nimport ToolDetailServer from '@/components/tools/ToolDetailServer';\nimport { apiClient } from '@/lib/api';\nimport { getToolStructuredData, getBreadcrumbStructuredData } from '@/lib/seo/structuredData';\nimport { Locale } from '@/i18n/config';\n\n// 生成动态metadata\nexport async function generateMetadata({ params }: { params: Promise<{ id: string; locale: Locale }> }): Promise<Metadata> {\n  try {\n    const { id, locale } = await params;\n    const t = await getTranslations({ locale, namespace: 'tool_detail' });\n\n    const response = await apiClient.getTool(id);\n\n    if (!response.success || !response.data) {\n      const siteT = await getTranslations({ locale, namespace: 'site' });\n      return {\n        title: `${t('not_found')} - ${siteT('title')}`,\n        description: t('not_found_desc'),\n      };\n    }\n\n    const tool = response.data;\n    const siteT = await getTranslations({ locale, namespace: 'site' });\n    const title = `${tool.name} - ${siteT('title')}`;\n    const description = tool.description || `${tool.name} is an excellent AI tool to boost your productivity.`;\n    const keywords = [tool.name, ...(tool.tags || []), 'AI tools', tool.category].join(', ');\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';\n    const canonical = `/tools/${tool._id}`;\n    const ogImage = tool.logo || '/og-tool-default.jpg';\n\n    return {\n      title,\n      description,\n      keywords,\n      authors: [{ name: siteT('title') }],\n      robots: {\n        index: true,\n        follow: true,\n      },\n      openGraph: {\n        type: 'article',\n        locale: locale === 'zh' ? 'zh_CN' : 'en_US',\n        url: `${baseUrl}${canonical}`,\n        siteName: siteT('title'),\n        title,\n        description,\n        images: [\n          {\n            url: ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`,\n            width: 1200,\n            height: 630,\n            alt: `${tool.name} - ${siteT('title')}`,\n          },\n        ],\n        publishedTime: tool.launchDate ? new Date(tool.launchDate).toISOString() : undefined,\n        modifiedTime: tool.updatedAt ? new Date(tool.updatedAt).toISOString() : undefined,\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title,\n        description,\n        images: [ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`],\n      },\n      alternates: {\n        canonical: `${baseUrl}${canonical}`,\n      },\n    };\n  } catch (error: unknown) {\n    const siteT = await getTranslations({ locale: 'zh', namespace: 'site' });\n    const toolT = await getTranslations({ locale: 'zh', namespace: 'tool_detail' });\n    return {\n      title: `${toolT('page_title')} - ${siteT('title')}`,\n      description: toolT('not_found_desc'),\n    };\n  }\n}\n\n// 服务器端渲染的工具详情页\nexport default async function ToolDetailPage({ params }: { params: Promise<{ id: string; locale: Locale }> }) {\n  try {\n    const { id, locale } = await params;\n    const t = await getTranslations({ locale, namespace: 'tool_detail' });\n\n    const response = await apiClient.getTool(id);\n\n    if (!response.success || !response.data) {\n      notFound();\n    }\n\n    const tool = response.data;\n\n    // 检查工具是否已发布：approved状态且launchDate已过\n    // const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();\n\n    // if (!isPublished) {\n    //   notFound();\n    // }\n\n    // 工具数据已经是正确的格式，无需额外序列化\n    const serializedTool = tool;\n\n    // 生成结构化数据\n    const toolStructuredData = getToolStructuredData(serializedTool);\n    const breadcrumbStructuredData = getBreadcrumbStructuredData([\n      { name: t('breadcrumb_home'), url: '/' },\n      { name: t('breadcrumb_tools'), url: '/tools' },\n      { name: tool.name, url: `/tools/${tool._id}` }\n    ]);\n\n    return (\n      <>\n        {/* 结构化数据 */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify(toolStructuredData)\n          }}\n        />\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify(breadcrumbStructuredData)\n          }}\n        />\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* 面包屑导航 */}\n          <nav className=\"flex items-center space-x-2 text-sm text-gray-500 mb-6\" aria-label={t('breadcrumb_aria_label')}>\n            <Link href=\"/\" className=\"hover:text-blue-600\">{t('breadcrumb_home')}</Link>\n            <span>/</span>\n            <Link href=\"/tools\" className=\"hover:text-blue-600\">{t('breadcrumb_tools')}</Link>\n            <span>/</span>\n            <span className=\"text-gray-900\">{tool.name}</span>\n          </nav>\n\n          {/* 返回按钮 */}\n          <div className=\"mb-6\">\n            <Link\n              href=\"/tools\"\n              className=\"inline-flex items-center text-blue-600 hover:text-blue-700\"\n            >\n              <svg className=\"mr-2 h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n              {t('back_to_tools')}\n            </Link>\n          </div>\n\n          {/* 工具详情服务端组件 */}\n          <ToolDetailServer initialTool={serializedTool} toolId={id} locale={locale} />\n        </div>\n      </>\n    );\n  } catch (error) {\n    console.error('Error loading tool:', error);\n    notFound();\n  }\n}\n"], "names": [], "mappings": ";;;;;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAIO,eAAe,iBAAiB,EAAE,MAAM,EAAuD;IACpG,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,MAAM;QAC7B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;YAAE;YAAQ,WAAW;QAAc;QAEnE,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;QAEzC,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,MAAM,QAAQ,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;gBAAE;gBAAQ,WAAW;YAAO;YAChE,OAAO;gBACL,OAAO,GAAG,EAAE,aAAa,GAAG,EAAE,MAAM,UAAU;gBAC9C,aAAa,EAAE;YACjB;QACF;QAEA,MAAM,OAAO,SAAS,IAAI;QAC1B,MAAM,QAAQ,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;YAAE;YAAQ,WAAW;QAAO;QAChE,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,MAAM,UAAU;QAChD,MAAM,cAAc,KAAK,WAAW,IAAI,GAAG,KAAK,IAAI,CAAC,oDAAoD,CAAC;QAC1G,MAAM,WAAW;YAAC,KAAK,IAAI;eAAM,KAAK,IAAI,IAAI,EAAE;YAAG;YAAY,KAAK,QAAQ;SAAC,CAAC,IAAI,CAAC;QACnF,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD,MAAM,YAAY,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;QACtC,MAAM,UAAU,KAAK,IAAI,IAAI;QAE7B,OAAO;YACL;YACA;YACA;YACA,SAAS;gBAAC;oBAAE,MAAM,MAAM;gBAAS;aAAE;YACnC,QAAQ;gBACN,OAAO;gBACP,QAAQ;YACV;YACA,WAAW;gBACT,MAAM;gBACN,QAAQ,WAAW,OAAO,UAAU;gBACpC,KAAK,GAAG,UAAU,WAAW;gBAC7B,UAAU,MAAM;gBAChB;gBACA;gBACA,QAAQ;oBACN;wBACE,KAAK,QAAQ,UAAU,CAAC,UAAU,UAAU,GAAG,UAAU,SAAS;wBAClE,OAAO;wBACP,QAAQ;wBACR,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,MAAM,UAAU;oBACzC;iBACD;gBACD,eAAe,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,UAAU,EAAE,WAAW,KAAK;gBAC3E,cAAc,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,WAAW,KAAK;YAC1E;YACA,SAAS;gBACP,MAAM;gBACN;gBACA;gBACA,QAAQ;oBAAC,QAAQ,UAAU,CAAC,UAAU,UAAU,GAAG,UAAU,SAAS;iBAAC;YACzE;YACA,YAAY;gBACV,WAAW,GAAG,UAAU,WAAW;YACrC;QACF;IACF,EAAE,OAAO,OAAgB;QACvB,MAAM,QAAQ,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;YAAE,QAAQ;YAAM,WAAW;QAAO;QACtE,MAAM,QAAQ,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;YAAE,QAAQ;YAAM,WAAW;QAAc;QAC7E,OAAO;YACL,OAAO,GAAG,MAAM,cAAc,GAAG,EAAE,MAAM,UAAU;YACnD,aAAa,MAAM;QACrB;IACF;AACF;AAGe,eAAe,eAAe,EAAE,MAAM,EAAuD;IAC1G,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,MAAM;QAC7B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;YAAE;YAAQ,WAAW;QAAc;QAEnE,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;QAEzC,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;QACT;QAEA,MAAM,OAAO,SAAS,IAAI;QAE1B,oCAAoC;QACpC,gHAAgH;QAEhH,sBAAsB;QACtB,gBAAgB;QAChB,IAAI;QAEJ,uBAAuB;QACvB,MAAM,iBAAiB;QAEvB,UAAU;QACV,MAAM,qBAAqB,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD,EAAE;QACjD,MAAM,2BAA2B,CAAA,GAAA,mIAAA,CAAA,8BAA2B,AAAD,EAAE;YAC3D;gBAAE,MAAM,EAAE;gBAAoB,KAAK;YAAI;YACvC;gBAAE,MAAM,EAAE;gBAAqB,KAAK;YAAS;YAC7C;gBAAE,MAAM,KAAK,IAAI;gBAAE,KAAK,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;YAAC;SAC9C;QAED,qBACE;;8BAEE,8OAAC;oBACC,MAAK;oBACL,yBAAyB;wBACvB,QAAQ,KAAK,SAAS,CAAC;oBACzB;;;;;;8BAEF,8OAAC;oBACC,MAAK;oBACL,yBAAyB;wBACvB,QAAQ,KAAK,SAAS,CAAC;oBACzB;;;;;;8BAGF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;4BAAyD,cAAY,EAAE;;8CACpF,8OAAC,sHAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAuB,EAAE;;;;;;8CAClD,8OAAC;8CAAK;;;;;;8CACN,8OAAC,sHAAA,CAAA,OAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAuB,EAAE;;;;;;8CACvD,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAU;8CAAiB,KAAK,IAAI;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sHAAA,CAAA,OAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCAEtE,EAAE;;;;;;;;;;;;sCAKP,8OAAC,+IAAA,CAAA,UAAgB;4BAAC,aAAa;4BAAgB,QAAQ;4BAAI,QAAQ;;;;;;;;;;;;;;IAI3E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;AACF", "debugId": null}}]}