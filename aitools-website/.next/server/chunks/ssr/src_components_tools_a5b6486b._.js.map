{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\nimport { useLike } from '@/contexts/LikeContext';\nimport { Locale } from '@/i18n/config';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean;\n  showCount?: boolean; // 是否显示点赞数量\n  size?: 'sm' | 'md' | 'lg'; // 按钮大小\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false,\n  showCount = true,\n  size = 'md'\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const { getToolState, initializeToolState, toggleLike } = useLike();\n\n  const pathname = usePathname();\n  const t = useTranslations('common');\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  // 获取当前工具的状态\n  const toolState = getToolState(toolId);\n\n  // 初始化工具状态\n  useEffect(() => {\n    initializeToolState(toolId, initialLikes, initialLiked);\n  }, [toolId, initialLikes, initialLiked]); // 移除initializeToolState依赖，避免无限循环\n\n  // 处理点赞点击\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (toolState.loading) return;\n\n    // 记录操作前的状态\n    const wasLiked = toolState.liked;\n\n    // 执行点赞操作\n    const success = await toggleLike(toolId, isInLikedPage);\n\n    // 如果是在收藏页面且从已点赞变为未点赞，调用onUnlike回调\n    if (success && isInLikedPage && wasLiked && onUnlike) {\n      onUnlike(toolId);\n    }\n  };\n\n  // 根据size确定样式\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return {\n          button: 'p-1.5',\n          icon: 'h-4 w-4',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          button: 'p-3',\n          icon: 'h-6 w-6',\n          text: 'text-lg'\n        };\n      default: // md\n        return {\n          button: 'p-2',\n          icon: 'h-5 w-5',\n          text: 'text-base'\n        };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={toolState.loading}\n      className={`\n        ${sizeClasses.button}\n        inline-flex items-center space-x-1\n        ${toolState.liked\n          ? 'text-red-500 hover:text-red-600'\n          : 'text-gray-400 hover:text-red-500'\n        }\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      `}\n      title={toolState.liked ? t('unlike') : t('like')}\n    >\n      {toolState.loading ? (\n        <div className={`${sizeClasses.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`} />\n      ) : toolState.liked ? (\n        <FaHeart className={sizeClasses.icon} />\n      ) : (\n        <FaRegHeart className={sizeClasses.icon} />\n      )}\n      {showCount && (\n        <span className={`${sizeClasses.text} font-medium`}>\n          {toolState.likes}\n        </span>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAqBe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACrB,YAAY,IAAI,EAChB,OAAO,IAAI,EACK;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhE,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,YAAY;IACZ,MAAM,YAAY,aAAa;IAE/B,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB,QAAQ,cAAc;IAC5C,GAAG;QAAC;QAAQ;QAAc;KAAa,GAAG,iCAAiC;IAE3E,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,UAAU,OAAO,EAAE;QAEvB,WAAW;QACX,MAAM,WAAW,UAAU,KAAK;QAEhC,SAAS;QACT,MAAM,UAAU,MAAM,WAAW,QAAQ;QAEzC,kCAAkC;QAClC,IAAI,WAAW,iBAAiB,YAAY,UAAU;YACpD,SAAS;QACX;IACF;IAEA,aAAa;IACb,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QACC,SAAS;QACT,UAAU,UAAU,OAAO;QAC3B,WAAW,CAAC;QACV,EAAE,YAAY,MAAM,CAAC;;QAErB,EAAE,UAAU,KAAK,GACb,oCACA,mCACH;;;MAGH,CAAC;QACD,OAAO,UAAU,KAAK,GAAG,EAAE,YAAY,EAAE;;YAExC,UAAU,OAAO,iBAChB,8OAAC;gBAAI,WAAW,GAAG,YAAY,IAAI,CAAC,oEAAoE,CAAC;;;;;uBACvG,UAAU,KAAK,iBACjB,8OAAC,8IAAA,CAAA,UAAO;gBAAC,WAAW,YAAY,IAAI;;;;;qCAEpC,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAW,YAAY,IAAI;;;;;;YAExC,2BACC,8OAAC;gBAAK,WAAW,GAAG,YAAY,IAAI,CAAC,YAAY,CAAC;0BAC/C,UAAU,KAAK;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useTranslations } from 'next-intl';\nimport { FaUser, FaR<PERSON>ly, <PERSON>aH<PERSON>t, FaRegHeart } from 'react-icons/fa';\n\ninterface Comment {\n  _id: string;\n  content: string;\n  userId: {\n    _id: string;\n    name: string;\n    email: string;\n    image?: string;\n  };\n  createdAt: string;\n  likes: number;\n  replies?: Comment[];\n}\n\ninterface CommentSectionClientProps {\n  toolId: string;\n  initialComments: Comment[];\n  onLoginRequired?: () => void;\n}\n\nexport default function CommentSectionClient({ \n  toolId, \n  initialComments, \n  onLoginRequired \n}: CommentSectionClientProps) {\n  const { data: session } = useSession();\n  const [comments, setComments] = useState<Comment[]>(initialComments);\n  const [newComment, setNewComment] = useState('');\n  const [replyTo, setReplyTo] = useState<string | null>(null);\n  const [replyContent, setReplyContent] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const t = useTranslations('comments');\n\n  // 重新获取评论列表\n  const fetchComments = async () => {\n    try {\n      const response = await fetch(`/api/tools/${toolId}/comments`);\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setComments(data.data.comments);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch comments:', error);\n    }\n  };\n\n  // 提交评论\n  const handleSubmitComment = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (!newComment.trim()) return;\n\n    setIsSubmitting(true);\n    try {\n      const response = await fetch(`/api/tools/${toolId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: newComment.trim()\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setNewComment('');\n          fetchComments(); // 重新获取评论列表\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Comment submission failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Comment submission error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 提交回复\n  const handleSubmitReply = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (!replyContent.trim() || !replyTo) return;\n\n    setIsSubmitting(true);\n    try {\n      const response = await fetch(`/api/tools/${toolId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: replyContent.trim(),\n          parentId: replyTo\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setReplyContent('');\n          setReplyTo(null);\n          fetchComments(); // 重新获取评论列表\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Reply submission failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Reply submission error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 格式化时间\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) {\n      return t('just_now');\n    } else if (diffInHours < 24) {\n      return t('hours_ago', { hours: diffInHours });\n    } else {\n      const diffInDays = Math.floor(diffInHours / 24);\n      if (diffInDays < 7) {\n        return t('days_ago', { days: diffInDays });\n      } else {\n        return date.toLocaleDateString();\n      }\n    }\n  };\n\n  // 渲染单个评论\n  const renderComment = (comment: Comment, isReply = false) => (\n    <div key={comment._id} className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"flex-shrink-0\">\n          {comment.userId.image ? (\n            <img\n              src={comment.userId.image}\n              alt={comment.userId.name}\n              className=\"w-8 h-8 rounded-full\"\n            />\n          ) : (\n            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n              <FaUser className=\"w-4 h-4 text-gray-600\" />\n            </div>\n          )}\n        </div>\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm font-medium text-gray-900\">\n              {comment.userId.name}\n            </span>\n            <span className=\"text-xs text-gray-500\">\n              {formatDate(comment.createdAt)}\n            </span>\n          </div>\n          <p className=\"mt-1 text-sm text-gray-700\">\n            {comment.content}\n          </p>\n          <div className=\"mt-2 flex items-center space-x-4\">\n            <button\n              className=\"text-xs text-gray-500 hover:text-blue-600 flex items-center space-x-1\"\n              onClick={() => setReplyTo(replyTo === comment._id ? null : comment._id)}\n            >\n              <FaReply className=\"w-3 h-3\" />\n              <span>{t('reply')}</span>\n            </button>\n          </div>\n          \n          {/* 回复输入框 */}\n          {replyTo === comment._id && (\n            <div className=\"mt-3 space-y-2\">\n              <textarea\n                value={replyContent}\n                onChange={(e) => setReplyContent(e.target.value)}\n                placeholder={session ? t('write_reply') : t('login_to_reply')}\n                className=\"w-full p-2 text-sm border border-gray-300 rounded resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={2}\n                maxLength={500}\n                disabled={!session}\n              />\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-xs text-gray-500\">\n                  {replyContent.length}/500\n                </span>\n                <div className=\"space-x-2\">\n                  <button\n                    onClick={() => {\n                      setReplyTo(null);\n                      setReplyContent('');\n                    }}\n                    className=\"px-3 py-1 text-xs text-gray-600 hover:text-gray-800\"\n                  >\n                    {t('cancel')}\n                  </button>\n                  <button\n                    onClick={handleSubmitReply}\n                    disabled={isSubmitting || !replyContent.trim() || !session}\n                    className=\"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isSubmitting ? t('submitting') : t('submit_reply')}\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n      \n      {/* 渲染回复 */}\n      {comment.replies && comment.replies.length > 0 && (\n        <div className=\"mt-4 space-y-4\">\n          {comment.replies.map(reply => renderComment(reply, true))}\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n        {t('comments')} ({comments.length})\n      </h3>\n      \n      {/* 评论输入框 */}\n      <div className=\"space-y-3 mb-6\">\n        <textarea\n          value={newComment}\n          onChange={(e) => setNewComment(e.target.value)}\n          placeholder={session ? t('write_comment') : t('login_to_comment')}\n          className=\"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          rows={4}\n          maxLength={1000}\n          disabled={!session}\n        />\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-sm text-gray-500\">\n            {newComment.length}/1000\n          </span>\n          <button\n            onClick={handleSubmitComment}\n            disabled={isSubmitting || !newComment.trim() || !session}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSubmitting ? t('submitting') : t('submit_comment')}\n          </button>\n        </div>\n      </div>\n\n      {/* 评论列表 */}\n      <div className=\"space-y-6\">\n        {comments.length === 0 ? (\n          <p className=\"text-gray-500 text-center py-8\">\n            {t('no_comments')}\n          </p>\n        ) : (\n          comments.map(comment => renderComment(comment))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AA2Be,SAAS,qBAAqB,EAC3C,MAAM,EACN,eAAe,EACf,eAAe,EACW;IAC1B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,WAAW;IACX,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC;YAC5D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,WAAW,IAAI;gBAC1B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc;oBACd,iBAAiB,WAAW;gBAC9B;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,8BAA8B,UAAU,OAAO;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,SAAS;QAEtC,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,aAAa,IAAI;oBAC1B,UAAU;gBACZ;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB;oBAChB,WAAW;oBACX,iBAAiB,WAAW;gBAC9B;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,4BAA4B,UAAU,OAAO;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,cAAc,GAAG;YACnB,OAAO,EAAE;QACX,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,EAAE,aAAa;gBAAE,OAAO;YAAY;QAC7C,OAAO;YACL,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;YAC5C,IAAI,aAAa,GAAG;gBAClB,OAAO,EAAE,YAAY;oBAAE,MAAM;gBAAW;YAC1C,OAAO;gBACL,OAAO,KAAK,kBAAkB;YAChC;QACF;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC,SAAkB,UAAU,KAAK,iBACtD,8OAAC;YAAsB,WAAW,GAAG,UAAU,yCAAyC,IAAI;;8BAC1F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,MAAM,CAAC,KAAK,iBACnB,8OAAC;gCACC,KAAK,QAAQ,MAAM,CAAC,KAAK;gCACzB,KAAK,QAAQ,MAAM,CAAC,IAAI;gCACxB,WAAU;;;;;qDAGZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAIxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,QAAQ,MAAM,CAAC,IAAI;;;;;;sDAEtB,8OAAC;4CAAK,WAAU;sDACb,WAAW,QAAQ,SAAS;;;;;;;;;;;;8CAGjC,8OAAC;oCAAE,WAAU;8CACV,QAAQ,OAAO;;;;;;8CAElB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,WAAW,YAAY,QAAQ,GAAG,GAAG,OAAO,QAAQ,GAAG;;0DAEtE,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;gCAKZ,YAAY,QAAQ,GAAG,kBACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,aAAa,UAAU,EAAE,iBAAiB,EAAE;4CAC5C,WAAU;4CACV,MAAM;4CACN,WAAW;4CACX,UAAU,CAAC;;;;;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDACb,aAAa,MAAM;wDAAC;;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS;gEACP,WAAW;gEACX,gBAAgB;4DAClB;4DACA,WAAU;sEAET,EAAE;;;;;;sEAEL,8OAAC;4DACC,SAAS;4DACT,UAAU,gBAAgB,CAAC,aAAa,IAAI,MAAM,CAAC;4DACnD,WAAU;sEAET,eAAe,EAAE,gBAAgB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUjD,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAA,QAAS,cAAc,OAAO;;;;;;;WAhF/C,QAAQ,GAAG;;;;;IAsFvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBACX,EAAE;oBAAY;oBAAG,SAAS,MAAM;oBAAC;;;;;;;0BAIpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,aAAa,UAAU,EAAE,mBAAmB,EAAE;wBAC9C,WAAU;wBACV,MAAM;wBACN,WAAW;wBACX,UAAU,CAAC;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCACb,WAAW,MAAM;oCAAC;;;;;;;0CAErB,8OAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB,CAAC,WAAW,IAAI,MAAM,CAAC;gCACjD,WAAU;0CAET,eAAe,EAAE,gBAAgB,EAAE;;;;;;;;;;;;;;;;;;0BAM1C,8OAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;oBAAE,WAAU;8BACV,EAAE;;;;;2BAGL,SAAS,GAAG,CAAC,CAAA,UAAW,cAAc;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport LoginModal from '@/components/auth/LoginModal';\n\ninterface ToolDetailInteractionProps {\n  children: React.ReactNode;\n}\n\nexport default function ToolDetailInteraction({ children }: ToolDetailInteractionProps) {\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n\n  const handleLoginRequired = () => {\n    setIsLoginModalOpen(true);\n  };\n\n  // 克隆children并传递回调函数\n  const childrenWithProps = React.Children.map(children, (child) => {\n    if (React.isValidElement(child)) {\n      return React.cloneElement(child, {\n        onLoginRequired: handleLoginRequired,\n      } as any);\n    }\n    return child;\n  });\n\n  return (\n    <>\n      {childrenWithProps}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,sBAAsB,EAAE,QAAQ,EAA8B;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,sBAAsB;QAC1B,oBAAoB;IACtB;IAEA,oBAAoB;IACpB,MAAM,oBAAoB,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC;QACtD,kBAAI,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,QAAQ;YAC/B,qBAAO,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,OAAO;gBAC/B,iBAAiB;YACnB;QACF;QACA,OAAO;IACT;IAEA,qBACE;;YACG;0BACD,8OAAC,wIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;AAI3C", "debugId": null}}]}