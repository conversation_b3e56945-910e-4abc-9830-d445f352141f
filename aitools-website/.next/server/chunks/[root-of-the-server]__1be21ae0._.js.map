{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // 构建时特殊处理：如果是构建阶段，使用固定的URL\n    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL && !process.env.NETLIFY) {\n      // 生产构建时，假设服务器将在3011端口运行\n      return 'http://localhost:3011';\n    }\n\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n\n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n\n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n\n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n\n    // 开发环境默认值\n    const port = process.env.PORT || '3011';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3011';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3011';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 获取AI服务URL配置\n */\nexport function getAiServiceUrls() {\n  return {\n    generateProductInfo: process.env.AI_GENERATE_PRODUCT_INFO_URL || 'http://localhost:50058/ai/generateProductInfo',\n  };\n}\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n  console.log('  AI Service URLs:', getAiServiceUrls());\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,2BAA2B;QAC3B,uCAA8F;;QAG9F;QAEA,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAKO,SAAS;IACd,OAAO;QACL,qBAAqB,QAAQ,GAAG,CAAC,4BAA4B,IAAI;IACnE;AACF;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;IACtC,QAAQ,GAAG,CAAC,sBAAsB;AACpC", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/ai/generate-product-info/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getAiServiceUrls } from '@/lib/env';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { website } = await request.json();\n\n    if (!website) {\n      return NextResponse.json(\n        { success: false, error: 'Website URL is required' },\n        { status: 400 }\n      );\n    }\n\n    // 验证URL格式\n    try {\n      new URL(website);\n    } catch (error) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid website URL format' },\n        { status: 400 }\n      );\n    }\n\n    const aiServiceUrls = getAiServiceUrls();\n    \n    // 调用AI服务\n    const response = await fetch(aiServiceUrls.generateProductInfo, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ data: { url: website }}),\n    });\n\n    if (!response.ok) {\n      console.error('AI service error:', response.status, response.statusText);\n      return NextResponse.json(\n        { success: false, error: 'AI service is currently unavailable' },\n        { status: 503 }\n      );\n    }\n\n    const aiResponse = await response.json();\n    \n    // 验证AI服务返回的数据格式\n    if (!aiResponse.data?.generated?.productInfo) {\n      console.error('Invalid AI response format:', aiResponse);\n      return NextResponse.json(\n        { success: false, error: 'Invalid response from AI service' },\n        { status: 502 }\n      );\n    }\n\n    const productInfo = aiResponse.data.generated.productInfo;\n    \n    // 验证必要字段\n    const requiredFields = ['name', 'tagline', 'description', 'category'];\n    const missingFields = requiredFields.filter(field => !productInfo[field]);\n    \n    if (missingFields.length > 0) {\n      console.error('Missing required fields in AI response:', missingFields);\n      return NextResponse.json(\n        { success: false, error: 'Incomplete data from AI service' },\n        { status: 502 }\n      );\n    }\n\n    // 返回处理后的数据\n    return NextResponse.json({\n      success: true,\n      data: {\n        name: productInfo.name || '',\n        tagline: productInfo.tagline || '',\n        description: productInfo.description || '',\n        category: productInfo.category || '',\n        tags: Array.isArray(productInfo.tags) ? productInfo.tags : [],\n      }\n    });\n\n  } catch (error) {\n    console.error('Generate product info error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0B,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI;YACF,IAAI,IAAI;QACV,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA6B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD;QAErC,SAAS;QACT,MAAM,WAAW,MAAM,MAAM,cAAc,mBAAmB,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,MAAM;oBAAE,KAAK;gBAAQ;YAAC;QAC/C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,qBAAqB,SAAS,MAAM,EAAE,SAAS,UAAU;YACvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsC,GAC/D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,MAAM,SAAS,IAAI;QAEtC,gBAAgB;QAChB,IAAI,CAAC,WAAW,IAAI,EAAE,WAAW,aAAa;YAC5C,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmC,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW;QAEzD,SAAS;QACT,MAAM,iBAAiB;YAAC;YAAQ;YAAW;YAAe;SAAW;QACrE,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,CAAC,MAAM;QAExE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,MAAM,YAAY,IAAI,IAAI;gBAC1B,SAAS,YAAY,OAAO,IAAI;gBAChC,aAAa,YAAY,WAAW,IAAI;gBACxC,UAAU,YAAY,QAAQ,IAAI;gBAClC,MAAM,MAAM,OAAO,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,GAAG,EAAE;YAC/D;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}