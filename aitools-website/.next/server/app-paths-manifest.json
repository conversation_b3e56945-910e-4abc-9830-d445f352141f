{"/[locale]/admin/page": "app/[locale]/admin/page.js", "/[locale]/page": "app/[locale]/page.js", "/[locale]/submit/page": "app/[locale]/submit/page.js", "/[locale]/tools/[id]/page": "app/[locale]/tools/[id]/page.js", "/_not-found/page": "app/_not-found/page.js", "/api/admin/tools/[id]/approve/route": "app/api/admin/tools/[id]/approve/route.js", "/api/admin/tools/route": "app/api/admin/tools/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/tools/[id]/like/route": "app/api/tools/[id]/like/route.js", "/api/tools/[id]/route": "app/api/tools/[id]/route.js", "/api/tools/route": "app/api/tools/route.js"}