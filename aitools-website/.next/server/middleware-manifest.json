{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__abf7a29f._.js", "server/edge/chunks/edge-wrapper_80fb5899.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "b4e9fb642eb2e42b19edee47aeba3319", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f7e23aedae676ac6d7df6acaeaedfe15b270450508063c2bcc2340a40b1a5184", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a9cd4b4997ed72182691d97ca0ae41f6a0ad4954acc08f6c0cf404f21925bb48"}}}, "sortedMiddleware": ["/"], "functions": {}}