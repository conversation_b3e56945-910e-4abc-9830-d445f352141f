{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__abf7a29f._.js", "server/edge/chunks/edge-wrapper_80fb5899.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "b39a6299105a9c09093490539354241e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "85a68839907da033ad5ed20392899f26f0442588480055ced6123cbafc3960db", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "40ba000bde97610ad955b3fc44bb3eb16d1ec852f2c6b97ca5a95a14691adfed"}}}, "instrumentation": null, "functions": {}}