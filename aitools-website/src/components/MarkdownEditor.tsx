'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { useTranslations } from 'next-intl';

// 动态导入 MDEditor 以避免 SSR 问题
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor').then((mod) => mod.default),
  { ssr: false }
);

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  error?: string;
  className?: string;
  height?: number;
}

export default function MarkdownEditor({
  value,
  onChange,
  placeholder,
  maxLength,
  error,
  className = '',
  height = 300
}: MarkdownEditorProps) {
  const t = useTranslations('form');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleChange = (val?: string) => {
    const newValue = val || '';
    
    // 检查最大长度限制
    if (maxLength && newValue.length > maxLength) {
      return;
    }
    
    onChange(newValue);
  };

  // 在服务端渲染时显示简单的 textarea
  if (!mounted) {
    return (
      <div className={className}>
        <textarea
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            error ? 'border-red-300' : 'border-gray-300'
          }`}
          style={{ height: `${height}px` }}
          maxLength={maxLength}
        />
        {maxLength && (
          <div className="mt-1 text-right text-sm text-gray-500">
            {value.length}/{maxLength}
          </div>
        )}
        {error && (
          <div className="mt-1 text-sm text-red-600">{error}</div>
        )}
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="markdown-editor-wrapper">
        <MDEditor
          value={value}
          onChange={handleChange}
          height={height}
          preview="edit"
          hideToolbar={false}
          visibleDragBar={false}
          textareaProps={{
            placeholder: placeholder || t('description_placeholder'),
            style: {
              fontSize: 14,
              lineHeight: 1.5,
              fontFamily: 'inherit',
            },
            maxLength: maxLength,
          }}
          data-color-mode="light"
        />
      </div>
      
      {/* 字符计数器 */}
      {maxLength && (
        <div className="mt-1 flex justify-between items-center text-sm">
          <div className="text-gray-500">
            支持 Markdown 格式：**粗体**、## 标题、- 列表
          </div>
          <div className={`${value.length > maxLength * 0.9 ? 'text-orange-600' : 'text-gray-500'}`}>
            {value.length}/{maxLength}
          </div>
        </div>
      )}
      
      {/* 错误信息 */}
      {error && (
        <div className="mt-1 text-sm text-red-600">{error}</div>
      )}
      
      <style jsx global>{`
        .markdown-editor-wrapper .w-md-editor {
          background-color: transparent;
        }
        
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-radius: 0.375rem;
          border: 1px solid #d1d5db;
        }
        
        .markdown-editor-wrapper .w-md-editor-text-container:focus-within {
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        
        .markdown-editor-wrapper .w-md-editor-text-input,
        .markdown-editor-wrapper .w-md-editor-text-textarea {
          font-size: 14px !important;
          line-height: 1.5 !important;
          padding: 12px !important;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar {
          border-bottom: 1px solid #e5e7eb;
          padding: 8px 12px;
          background-color: #f9fafb;
          border-radius: 0.375rem 0.375rem 0 0;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar-divider {
          background-color: #e5e7eb;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar button {
          color: #6b7280;
          border-radius: 0.25rem;
          padding: 4px 6px;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar button:hover {
          background-color: #e5e7eb;
          color: #374151;
        }
        
        .markdown-editor-wrapper .w-md-editor-toolbar button.active {
          background-color: #dbeafe;
          color: #1d4ed8;
        }
        
        ${error ? `
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        ` : ''}
      `}</style>
    </div>
  );
}
